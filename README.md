# 🏙️ 城市地图生成器 (City Map Generator) v2.0

一个基于Python的随机城市地图生成器，使用组件化设计生成包含道路、建筑、公园、水体和公共交通的完整城市地图。

## ✨ 特性

### 🏗️ 真实的城市发展模式
- 🛣️ **智能道路网络** - 先生成道路骨架，建筑沿路发展
- 🏢 **沿路建筑分布** - 建筑物优先在道路附近生成，形成真实街区
- 🗺️ **城市功能分区** - 商业区、住宅区、工业区等合理分布
- 🚇 **公共交通系统** - 地铁、公交、有轨电车、轻轨网络

### 🎯 智能生成算法
- 🌳 **绿地系统** - 公园、花园、游乐场等绿色空间
- 💧 **水体生成** - 湖泊、河流、池塘等自然水体
- 🎨 **精美可视化** - 使用matplotlib生成高质量地图图像
- ⚙️ **高度可配置** - 丰富的参数设置，支持自定义配置
- 🚀 **一键启动** - 提供交互式界面和命令行工具

## 📦 安装

### 环境要求

- Python 3.7+
- numpy >= 1.21.0
- matplotlib >= 3.5.0
- Pillow >= 8.3.0

### 快速安装

```bash
# 克隆项目
git clone <repository-url>
cd MapGen

# 安装依赖
pip install -r requirements.txt
```

## 🚀 快速开始

### 方法1: 一键启动 (推荐)

```bash
python run.py
```

这将启动交互式界面，按照提示选择即可。

### 方法2: 命令行使用

```bash
# 生成默认城市
python main.py

# 使用预设配置
python main.py --config small    # 小城市
python main.py --config dense    # 密集城市
python main.py --config green    # 绿色城市

# 自定义参数
python main.py --width 800 --height 600 --name "我的城市"

# 显示城市分区
python main.py --show-zones

# 批量生成
python main.py --multiple 5
```

### 方法3: 编程接口

```python
from city_map_generator import CityMapGenerator, MapConfig

# 创建配置
config = MapConfig()
config.width = 600
config.height = 600
config.name = "测试城市"

# 生成城市
generator = CityMapGenerator(config)
city_map = generator.generate_city()

# 可视化
generator.visualize_city(city_map)

# 保存图片
generator.save_city_image(city_map, "my_city.png")
```

## 📋 配置选项

### 预设配置

- `small` - 小城市 (300x300)
- `medium` - 中等城市 (500x500) 
- `large` - 大城市 (800x800)
- `dense` - 密集城市 (高建筑密度)
- `green` - 绿色城市 (更多公园)

### 主要参数

| 参数 | 说明 | 默认值 | 范围 |
|------|------|--------|------|
| `width` | 地图宽度 | 500.0 | > 0 |
| `height` | 地图高度 | 500.0 | > 0 |
| `building_density` | 建筑密度 | 0.4 | 0.0-1.0 |
| `park_density` | 公园密度 | 0.15 | 0.0-1.0 |
| `road_density` | 道路密度 | 0.3 | 0.0-1.0 |
| `random_seed` | 随机种子 | None | 整数 |

## 🏗️ 项目结构

```
MapGen/
├── city_map_generator/          # 核心模块
│   ├── __init__.py
│   ├── map.py                   # 地图核心类
│   ├── components.py            # 城市组件定义（含公交组件）
│   ├── config.py                # 配置系统
│   ├── generator.py             # 主生成器
│   ├── visualizer.py            # 可视化器（含分区显示）
│   ├── zoning.py                # 城市分区系统
│   └── generators/              # 各类组件生成器
│       ├── __init__.py
│       ├── road_generator.py    # 道路生成器
│       ├── building_generator.py # 建筑生成器（沿路分布）
│       ├── park_generator.py    # 公园生成器
│       ├── water_generator.py   # 水体生成器
│       └── transit_generator.py # 公共交通生成器
├── main.py                      # 命令行主程序
├── run.py                       # 一键启动脚本
├── requirements.txt             # 依赖列表
└── README.md                    # 说明文档
```

## 🎯 使用示例

### 生成不同类型的城市

```bash
# 生成小型住宅区
python main.py --config small --building-density 0.3 --park-density 0.2

# 生成商业中心（显示分区）
python main.py --building-density 0.8 --park-density 0.1 --road-density 0.4 --show-zones

# 生成绿色生态城市
python main.py --config green --park-density 0.3 --building-density 0.2

# 生成大城市（含完整公共交通）
python main.py --config large --show-zones
```

### 批量生成和比较

```bash
# 生成10个不同的城市进行比较
python main.py --multiple 10 --output ./city_comparison
```

### 使用配置文件

```python
from city_map_generator.config import MapConfig

# 创建自定义配置
config = MapConfig()
config.buildings.residential_ratio = 0.8  # 80%住宅
config.buildings.commercial_ratio = 0.15  # 15%商业
config.buildings.industrial_ratio = 0.05  # 5%工业

# 保存配置
config.save_to_file("my_config.json")

# 使用配置生成
python main.py --load-config my_config.json
```

## 🎨 可视化特性

生成的地图包含：

- 🗺️ **城市分区** - 可选显示功能分区边界和标签
- 🛣️ **道路系统** - 不同宽度和颜色的道路网络
- 🚇 **公共交通** - 地铁、公交线路和站点
- 🏠 **建筑物** - 按类型着色的建筑，高楼有阴影效果
- 🌳 **绿地** - 公园内有树木装饰
- 💧 **水体** - 湖泊、河流带有波纹效果
- 📊 **统计信息** - 显示城市组件统计
- 🏷️ **标签** - 重要建筑和区域标注
- 🎨 **图例** - 颜色说明

## 🔧 高级功能

### 组件化设计

每个城市元素都是独立的组件，支持：
- 自定义属性设置
- 空间索引查询
- 碰撞检测
- 动态添加/删除

### 智能布局算法

- 道路网络自动连接
- 建筑物避让道路和水体
- 公园优先选择环境友好位置
- 布局优化减少拥挤

### 扩展性

可以轻松添加新的组件类型：
1. 继承`Component`基类
2. 实现`render_data()`方法
3. 创建对应的生成器
4. 在可视化器中添加绘制逻辑

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者！
