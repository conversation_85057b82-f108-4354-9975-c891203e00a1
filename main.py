#!/usr/bin/env python3
"""
城市地图生成器 - 主程序

使用示例:
    python main.py                          # 使用默认配置生成城市
    python main.py --config small           # 使用小城市配置
    python main.py --multiple 5             # 生成5个不同的城市
    python main.py --width 800 --height 600 # 自定义尺寸
"""

import argparse
import sys
import os
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from city_map_generator import CityMapGenerator, MapConfig
from city_map_generator.config import (
    SMALL_CITY_CONFIG, MEDIUM_CITY_CONFIG, LARGE_CITY_CONFIG,
    DENSE_CITY_CONFIG, GREEN_CITY_CONFIG, MEGA_DENSE_CONFIG
)


def create_custom_config(args) -> MapConfig:
    """根据命令行参数创建自定义配置"""
    config = MapConfig()
    
    # 基本参数
    if args.width:
        config.width = args.width
    if args.height:
        config.height = args.height
    if args.name:
        config.name = args.name
    if args.seed:
        config.random_seed = args.seed
    
    # 密度参数
    if args.building_density:
        config.buildings.building_density = args.building_density
    if args.park_density:
        config.parks.park_density = args.park_density
    if args.road_density:
        config.roads.street_density = args.road_density
    
    return config


def get_preset_config(preset_name: str) -> Optional[MapConfig]:
    """获取预设配置"""
    presets = {
        'small': SMALL_CITY_CONFIG,
        'medium': MEDIUM_CITY_CONFIG,
        'large': LARGE_CITY_CONFIG,
        'dense': DENSE_CITY_CONFIG,
        'green': GREEN_CITY_CONFIG,
        'mega': MEGA_DENSE_CONFIG
    }
    return presets.get(preset_name.lower())


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="城市地图生成器 - 生成随机的组件化城市地图",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  %(prog)s                                    # 默认配置
  %(prog)s --config small                     # 小城市
  %(prog)s --config dense                     # 密集城市
  %(prog)s --width 800 --height 600           # 自定义尺寸
  %(prog)s --multiple 3                       # 生成3个城市
  %(prog)s --name "我的城市" --seed 12345      # 自定义名称和种子
  %(prog)s --no-show --output ./maps          # 不显示，保存到指定目录
        """
    )
    
    # 基本参数
    parser.add_argument('--config', '-c', type=str,
                       choices=['small', 'medium', 'large', 'dense', 'green', 'mega'],
                       help='使用预设配置')
    parser.add_argument('--width', '-w', type=float, help='地图宽度')
    parser.add_argument('--height', type=float, help='地图高度')
    parser.add_argument('--name', '-n', type=str, help='城市名称')
    parser.add_argument('--seed', '-s', type=int, help='随机种子')
    
    # 密度参数
    parser.add_argument('--building-density', type=float, 
                       help='建筑密度 (0.0-1.0)')
    parser.add_argument('--park-density', type=float,
                       help='公园密度 (0.0-1.0)')
    parser.add_argument('--road-density', type=float,
                       help='道路密度 (0.0-1.0)')
    
    # 输出参数
    parser.add_argument('--output', '-o', type=str, default='output',
                       help='输出目录 (默认: output)')
    parser.add_argument('--no-show', action='store_true',
                       help='不显示可视化窗口')
    parser.add_argument('--no-save', action='store_true',
                       help='不保存文件')
    parser.add_argument('--show-zones', action='store_true',
                       help='显示城市分区')
    
    # 批量生成
    parser.add_argument('--multiple', '-m', type=int,
                       help='生成多个城市的数量')
    
    # 配置文件
    parser.add_argument('--load-config', type=str,
                       help='从文件加载配置')
    parser.add_argument('--save-config', action='store_true',
                       help='保存配置到文件')
    
    args = parser.parse_args()
    
    try:
        # 确定配置
        if args.load_config:
            print(f"从文件加载配置: {args.load_config}")
            config = MapConfig.load_from_file(args.load_config)
        elif args.config:
            print(f"使用预设配置: {args.config}")
            config = get_preset_config(args.config)
            if config is None:
                print(f"错误: 未知的预设配置 '{args.config}'")
                return 1
        else:
            print("使用自定义配置")
            config = create_custom_config(args)
        
        # 创建生成器
        generator = CityMapGenerator(config)
        
        # 批量生成
        if args.multiple:
            print(f"开始生成 {args.multiple} 个城市...")
            cities = generator.generate_multiple_cities(
                count=args.multiple,
                output_dir=args.output
            )
            print(f"成功生成 {len(cities)} 个城市!")
            return 0
        
        # 单个城市生成
        print("开始生成城市...")
        
        if args.no_save:
            # 只生成不保存
            city_map = generator.generate_city()
            if not args.no_show:
                generator.visualize_city(city_map)
        else:
            # 生成并保存
            city_map = generator.generate_and_save(
                output_dir=args.output,
                show_visualization=not args.no_show,
                save_config=args.save_config,
                show_zones=args.show_zones
            )
        
        print("城市生成完成!")
        return 0
        
    except KeyboardInterrupt:
        print("\n用户中断操作")
        return 1
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
