"""
地图可视化器

使用matplotlib创建城市地图的可视化
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.collections import LineCollection
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
import os

from .map import CityMap
from .components import ComponentType, Road, Building, Park, Water


class MapVisualizer:
    """地图可视化器"""
    
    def __init__(self, figsize: Tuple[int, int] = (12, 12), dpi: int = 100):
        self.figsize = figsize
        self.dpi = dpi
        self.fig = None
        self.ax = None
    
    def visualize_map(self, city_map: CityMap, title: Optional[str] = None, 
                     show_labels: bool = True, show_stats: bool = True) -> plt.Figure:
        """可视化城市地图"""
        # 创建图形
        self.fig, self.ax = plt.subplots(figsize=self.figsize, dpi=self.dpi)
        
        # 设置背景色
        self.ax.set_facecolor('#F5F5DC')  # 米色背景
        
        # 绘制各种组件
        self._draw_water_bodies(city_map)
        self._draw_parks(city_map)
        self._draw_roads(city_map)
        self._draw_buildings(city_map)
        
        # 设置坐标轴
        self.ax.set_xlim(0, city_map.width)
        self.ax.set_ylim(0, city_map.height)
        self.ax.set_aspect('equal')
        
        # 设置标题
        if title is None:
            title = f"{city_map.name} - Generated City Map"
        self.ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        
        # 添加网格
        self.ax.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
        
        # 添加标签
        if show_labels:
            self._add_labels(city_map)
        
        # 添加统计信息
        if show_stats:
            self._add_statistics(city_map)
        
        # 添加图例
        self._add_legend()
        
        plt.tight_layout()
        return self.fig
    
    def _draw_roads(self, city_map: CityMap):
        """绘制道路"""
        roads = city_map.get_components_by_type(ComponentType.ROAD)
        
        # 按道路类型分组
        road_groups = {'highway': [], 'avenue': [], 'street': []}
        
        for road in roads:
            road_data = road.render_data()
            road_type = road_data.get('road_type', 'street')
            
            line = [(road_data['start'][0], road_data['start'][1]),
                   (road_data['end'][0], road_data['end'][1])]
            road_groups[road_type].append((line, road_data['width']))
        
        # 绘制不同类型的道路
        colors = {'highway': '#404040', 'avenue': '#606060', 'street': '#808080'}
        z_orders = {'highway': 3, 'avenue': 2, 'street': 1}
        
        for road_type, road_list in road_groups.items():
            if road_list:
                lines = [item[0] for item in road_list]
                widths = [item[1] for item in road_list]
                
                lc = LineCollection(lines, colors=colors[road_type], 
                                  linewidths=widths, zorder=z_orders[road_type],
                                  alpha=0.8)
                self.ax.add_collection(lc)
    
    def _draw_buildings(self, city_map: CityMap):
        """绘制建筑物"""
        buildings = city_map.get_components_by_type(ComponentType.BUILDING)
        
        for building in buildings:
            building_data = building.render_data()
            
            # 创建矩形
            rect = patches.Rectangle(
                building_data['position'],
                building_data['width'],
                building_data['height'],
                facecolor=building_data['color'],
                edgecolor='black',
                linewidth=0.5,
                alpha=0.8,
                zorder=4
            )
            self.ax.add_patch(rect)
            
            # 为高楼添加阴影效果
            if building.floors > 10:
                shadow_offset = building.floors * 0.1
                shadow_rect = patches.Rectangle(
                    (building_data['position'][0] + shadow_offset,
                     building_data['position'][1] - shadow_offset),
                    building_data['width'],
                    building_data['height'],
                    facecolor='gray',
                    alpha=0.3,
                    zorder=3
                )
                self.ax.add_patch(shadow_rect)
    
    def _draw_parks(self, city_map: CityMap):
        """绘制公园"""
        parks = city_map.get_components_by_type(ComponentType.PARK)
        
        for park in parks:
            park_data = park.render_data()
            
            # 创建公园矩形
            rect = patches.Rectangle(
                park_data['position'],
                park_data['width'],
                park_data['height'],
                facecolor=park_data['color'],
                edgecolor='darkgreen',
                linewidth=1,
                alpha=0.7,
                zorder=2
            )
            self.ax.add_patch(rect)
            
            # 添加树木效果（小圆点）
            if park.width > 20 and park.height > 20:
                tree_count = int((park.width * park.height) / 100)
                for _ in range(min(tree_count, 20)):
                    tree_x = park.position.x + np.random.uniform(2, park.width - 2)
                    tree_y = park.position.y + np.random.uniform(2, park.height - 2)
                    
                    circle = patches.Circle(
                        (tree_x, tree_y), 1.5,
                        facecolor='darkgreen',
                        alpha=0.6,
                        zorder=3
                    )
                    self.ax.add_patch(circle)
    
    def _draw_water_bodies(self, city_map: CityMap):
        """绘制水体"""
        water_bodies = city_map.get_components_by_type(ComponentType.WATER)
        
        for water in water_bodies:
            water_data = water.render_data()
            
            # 创建水体形状
            if water.water_type == "river":
                # 河流用圆角矩形
                rect = patches.FancyBboxPatch(
                    water_data['position'],
                    water_data['width'],
                    water_data['height'],
                    boxstyle="round,pad=0.5",
                    facecolor=water_data['color'],
                    edgecolor='navy',
                    linewidth=1,
                    alpha=0.8,
                    zorder=1
                )
            else:
                # 湖泊和池塘用椭圆
                ellipse = patches.Ellipse(
                    (water_data['position'][0] + water_data['width']/2,
                     water_data['position'][1] + water_data['height']/2),
                    water_data['width'],
                    water_data['height'],
                    facecolor=water_data['color'],
                    edgecolor='navy',
                    linewidth=1,
                    alpha=0.8,
                    zorder=1
                )
                rect = ellipse
            
            self.ax.add_patch(rect)
            
            # 添加水波纹效果
            if water.width > 15 and water.height > 15:
                center_x = water.position.x + water.width / 2
                center_y = water.position.y + water.height / 2
                
                for i in range(2, 4):
                    ripple = patches.Circle(
                        (center_x, center_y), i * 3,
                        fill=False,
                        edgecolor='lightblue',
                        alpha=0.4,
                        linewidth=0.5,
                        zorder=2
                    )
                    self.ax.add_patch(ripple)
    
    def _add_labels(self, city_map: CityMap):
        """添加标签"""
        # 为大型组件添加标签
        components = city_map.components.values()
        
        for component in components:
            bounds = component.get_bounds()
            
            # 只为较大的组件添加标签
            if bounds.width * bounds.height > 400:
                center_x = bounds.x + bounds.width / 2
                center_y = bounds.y + bounds.height / 2
                
                label = ""
                if component.type == ComponentType.PARK:
                    label = f"Park\n{component.park_type}"
                elif component.type == ComponentType.WATER:
                    label = f"{component.water_type.title()}"
                elif component.type == ComponentType.BUILDING and hasattr(component, 'floors'):
                    if component.floors > 15:
                        label = f"{component.building_type.title()}\n{component.floors}F"
                
                if label:
                    self.ax.text(center_x, center_y, label,
                               ha='center', va='center',
                               fontsize=8, fontweight='bold',
                               bbox=dict(boxstyle="round,pad=0.3", 
                                       facecolor='white', alpha=0.7),
                               zorder=10)
    
    def _add_statistics(self, city_map: CityMap):
        """添加统计信息"""
        stats = city_map.get_stats()
        
        stats_text = f"""City Statistics:
Buildings: {stats['buildings_count']}
Parks: {stats['parks_count']}
Water Bodies: {stats['water_count']}
Roads Length: {stats['roads_length']:.1f}m
Total Components: {stats['total_components']}"""
        
        self.ax.text(0.02, 0.98, stats_text,
                    transform=self.ax.transAxes,
                    verticalalignment='top',
                    bbox=dict(boxstyle="round,pad=0.5", 
                            facecolor='white', alpha=0.8),
                    fontsize=10,
                    zorder=15)
    
    def _add_legend(self):
        """添加图例"""
        legend_elements = [
            patches.Patch(color='#8FBC8F', label='Residential'),
            patches.Patch(color='#4169E1', label='Commercial'),
            patches.Patch(color='#A0522D', label='Industrial'),
            patches.Patch(color='#DC143C', label='Public'),
            patches.Patch(color='#228B22', label='Parks'),
            patches.Patch(color='#4682B4', label='Water'),
            patches.Patch(color='#404040', label='Roads')
        ]
        
        self.ax.legend(handles=legend_elements, 
                      loc='upper right',
                      bbox_to_anchor=(0.98, 0.98),
                      fontsize=9)
    
    def save_map(self, filename: str, city_map: CityMap, **kwargs):
        """保存地图到文件"""
        if self.fig is None:
            self.visualize_map(city_map, **kwargs)
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filename) if os.path.dirname(filename) else '.', exist_ok=True)
        
        self.fig.savefig(filename, dpi=self.dpi, bbox_inches='tight', 
                        facecolor='white', edgecolor='none')
        print(f"Map saved to: {filename}")
    
    def show_map(self, city_map: CityMap, **kwargs):
        """显示地图"""
        if self.fig is None:
            self.visualize_map(city_map, **kwargs)
        
        plt.show()
    
    def close(self):
        """关闭图形"""
        if self.fig:
            plt.close(self.fig)
            self.fig = None
            self.ax = None
