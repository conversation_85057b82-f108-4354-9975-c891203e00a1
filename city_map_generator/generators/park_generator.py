"""
公园生成器

实现公园和绿地的生成算法
"""

import random
import numpy as np
from typing import List, Tuple, Optional
from ..components import Point, Park, ComponentType, Rectangle
from ..map import CityMap
from ..config import ParkConfig


class ParkGenerator:
    """公园生成器"""
    
    def __init__(self, config: ParkConfig):
        self.config = config
        self.park_counter = 0
    
    def generate_parks(self, city_map: CityMap) -> List[Park]:
        """生成公园"""
        parks = []
        
        # 计算目标公园数量
        target_park_count = random.randint(self.config.min_parks_count, self.config.max_parks_count)
        
        # 计算目标公园总面积
        total_area = city_map.width * city_map.height
        target_park_area = total_area * self.config.park_density
        
        current_area = 0
        attempts = 0
        max_attempts = 500
        
        while len(parks) < target_park_count and current_area < target_park_area and attempts < max_attempts:
            attempts += 1
            
            # 选择公园类型和尺寸
            park_type, width, height = self._generate_park_specs()
            
            # 寻找合适的位置
            position = self._find_park_position(city_map, width, height)
            
            if position:
                park = Park(
                    f"park_{self.park_counter}",
                    position,
                    width, height
                )
                park.park_type = park_type
                
                # 设置公园属性
                self._set_park_properties(park, park_type)
                
                # 尝试添加到地图
                if city_map.add_component(park):
                    parks.append(park)
                    current_area += width * height
                    self.park_counter += 1
        
        return parks
    
    def _generate_park_specs(self) -> Tuple[str, float, float]:
        """生成公园规格"""
        # 选择公园类型
        rand = random.random()
        if rand < self.config.large_park_probability:
            park_type = "large_park"
        elif rand < self.config.large_park_probability + self.config.playground_probability:
            park_type = "playground"
        else:
            park_type = "garden"
        
        # 根据类型生成尺寸
        min_w, min_h = self.config.min_park_size
        max_w, max_h = self.config.max_park_size
        
        if park_type == "large_park":
            # 大型公园
            min_w *= 1.5
            min_h *= 1.5
            width = random.uniform(min_w, max_w)
            height = random.uniform(min_h, max_h)
        elif park_type == "playground":
            # 游乐场，通常较小且接近正方形
            size = random.uniform(min_w * 0.7, min_w * 1.2)
            width = height = size
        else:  # garden
            # 花园，中等尺寸
            width = random.uniform(min_w, max_w * 0.8)
            height = random.uniform(min_h, max_h * 0.8)
        
        return park_type, width, height
    
    def _find_park_position(self, city_map: CityMap, width: float, height: float) -> Optional[Point]:
        """寻找公园位置"""
        max_attempts = 100
        
        for _ in range(max_attempts):
            # 随机选择位置
            x = random.uniform(0, city_map.width - width)
            y = random.uniform(0, city_map.height - height)
            
            position = Point(x, y)
            test_bounds = Rectangle(x, y, width, height)
            
            # 检查是否与现有组件冲突
            if not city_map.check_collision(test_bounds):
                # 额外检查：公园应该远离工业建筑
                if self._is_good_park_location(city_map, test_bounds):
                    return position
        
        return None
    
    def _is_good_park_location(self, city_map: CityMap, park_bounds: Rectangle) -> bool:
        """检查是否是好的公园位置"""
        # 检查周围是否有工业建筑
        buffer = 20.0
        expanded_bounds = Rectangle(
            park_bounds.x - buffer,
            park_bounds.y - buffer,
            park_bounds.width + 2 * buffer,
            park_bounds.height + 2 * buffer
        )
        
        nearby_components = city_map.get_components_in_area(expanded_bounds)
        
        for component in nearby_components:
            if (component.type == ComponentType.BUILDING and 
                hasattr(component, 'building_type') and 
                component.building_type == "industrial"):
                return False
        
        return True
    
    def _set_park_properties(self, park: Park, park_type: str):
        """设置公园属性"""
        park.park_type = park_type
        
        if park_type == "large_park":
            park.set_property("facilities", random.sample([
                "walking_trails", "lake", "playground", "picnic_areas", 
                "sports_fields", "gardens", "pavilion", "restrooms"
            ], k=random.randint(3, 6)))
            park.set_property("trees_count", random.randint(100, 500))
            park.set_property("maintenance_level", "high")
            
        elif park_type == "playground":
            park.set_property("facilities", random.sample([
                "swings", "slides", "climbing_frame", "sandbox", 
                "seesaw", "basketball_court", "benches"
            ], k=random.randint(3, 5)))
            park.set_property("age_group", random.choice(["toddler", "children", "all_ages"]))
            park.set_property("safety_rating", random.choice(["excellent", "good", "fair"]))
            
        else:  # garden
            park.set_property("facilities", random.sample([
                "flower_beds", "fountain", "benches", "walking_paths", 
                "gazebo", "herb_garden", "rose_garden"
            ], k=random.randint(2, 4)))
            park.set_property("garden_style", random.choice([
                "formal", "informal", "japanese", "english", "modern"
            ]))
            park.set_property("seasonal_flowers", True)
        
        # 通用属性
        park.set_property("established_year", random.randint(1960, 2020))
        park.set_property("visitor_capacity", int(park.width * park.height / 10))
        park.set_property("lighting", random.choice([True, False]))
        park.set_property("accessibility", random.choice([True, False]))
    
    def generate_park_system(self, city_map: CityMap) -> List[Park]:
        """生成连接的公园系统"""
        parks = []
        
        # 创建一个大型中央公园
        center_x = city_map.width / 2
        center_y = city_map.height / 2
        
        # 中央公园
        central_park_size = min(city_map.width, city_map.height) * 0.15
        central_position = Point(
            center_x - central_park_size / 2,
            center_y - central_park_size / 2
        )
        
        central_bounds = Rectangle(
            central_position.x, central_position.y,
            central_park_size, central_park_size
        )
        
        if not city_map.check_collision(central_bounds):
            central_park = Park(
                f"central_park_{self.park_counter}",
                central_position,
                central_park_size, central_park_size
            )
            central_park.park_type = "large_park"
            central_park.set_property("is_central_park", True)
            self._set_park_properties(central_park, "large_park")
            
            if city_map.add_component(central_park):
                parks.append(central_park)
                self.park_counter += 1
        
        # 在城市四个角落创建小公园
        corner_positions = [
            (city_map.width * 0.1, city_map.height * 0.1),
            (city_map.width * 0.9, city_map.height * 0.1),
            (city_map.width * 0.1, city_map.height * 0.9),
            (city_map.width * 0.9, city_map.height * 0.9)
        ]
        
        for x, y in corner_positions:
            park_size = 30.0
            position = Point(x - park_size/2, y - park_size/2)
            
            # 确保在边界内
            position.x = max(0, min(city_map.width - park_size, position.x))
            position.y = max(0, min(city_map.height - park_size, position.y))
            
            test_bounds = Rectangle(position.x, position.y, park_size, park_size)
            
            if not city_map.check_collision(test_bounds):
                corner_park = Park(
                    f"corner_park_{self.park_counter}",
                    position,
                    park_size, park_size
                )
                corner_park.park_type = "garden"
                self._set_park_properties(corner_park, "garden")
                
                if city_map.add_component(corner_park):
                    parks.append(corner_park)
                    self.park_counter += 1
        
        return parks
