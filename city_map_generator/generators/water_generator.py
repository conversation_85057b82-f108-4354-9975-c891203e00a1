"""
水体生成器

实现湖泊、河流等水体的生成算法
"""

import random
import numpy as np
from typing import List, Tuple, Optional
from ..components import Point, Water, ComponentType, Rectangle
from ..map import CityMap
from ..config import WaterConfig


class WaterGenerator:
    """水体生成器"""
    
    def __init__(self, config: WaterConfig):
        self.config = config
        self.water_counter = 0
    
    def generate_water_bodies(self, city_map: CityMap) -> List[Water]:
        """生成水体"""
        water_bodies = []
        
        # 检查是否生成水体
        if random.random() > self.config.water_probability:
            return water_bodies
        
        # 确定水体数量
        water_count = random.randint(self.config.min_water_bodies, self.config.max_water_bodies)
        
        for _ in range(water_count):
            # 选择水体类型和尺寸
            water_type, width, height = self._generate_water_specs()
            
            # 寻找合适的位置
            position = self._find_water_position(city_map, width, height, water_type)
            
            if position:
                water = Water(
                    f"water_{self.water_counter}",
                    position,
                    width, height
                )
                water.water_type = water_type
                
                # 设置水体属性
                self._set_water_properties(water, water_type)
                
                # 尝试添加到地图
                if city_map.add_component(water):
                    water_bodies.append(water)
                    self.water_counter += 1
        
        return water_bodies
    
    def _generate_water_specs(self) -> Tuple[str, float, float]:
        """生成水体规格"""
        # 选择水体类型
        rand = random.random()
        if rand < self.config.lake_probability:
            water_type = "lake"
        elif rand < self.config.lake_probability + self.config.river_probability:
            water_type = "river"
        else:
            water_type = "pond"
        
        # 根据类型生成尺寸
        min_w, min_h = self.config.min_water_size
        max_w, max_h = self.config.max_water_size
        
        if water_type == "lake":
            # 湖泊，较大且接近圆形
            base_size = random.uniform(min_w * 1.2, max_w)
            width = base_size * random.uniform(0.8, 1.2)
            height = base_size * random.uniform(0.8, 1.2)
            
        elif water_type == "river":
            # 河流，长而窄
            if random.choice([True, False]):  # 水平河流
                width = random.uniform(max_w * 0.8, max_w)
                height = random.uniform(min_h * 0.3, min_h * 0.8)
            else:  # 垂直河流
                width = random.uniform(min_w * 0.3, min_w * 0.8)
                height = random.uniform(max_h * 0.8, max_h)
                
        else:  # pond
            # 池塘，小而圆
            size = random.uniform(min_w * 0.6, min_w)
            width = height = size
        
        return water_type, width, height
    
    def _find_water_position(self, city_map: CityMap, width: float, height: float, 
                            water_type: str) -> Optional[Point]:
        """寻找水体位置"""
        max_attempts = 100
        
        for _ in range(max_attempts):
            if water_type == "river":
                # 河流倾向于穿越地图
                if width > height:  # 水平河流
                    x = random.uniform(0, city_map.width - width)
                    y = random.uniform(city_map.height * 0.2, city_map.height * 0.8 - height)
                else:  # 垂直河流
                    x = random.uniform(city_map.width * 0.2, city_map.width * 0.8 - width)
                    y = random.uniform(0, city_map.height - height)
            else:
                # 湖泊和池塘可以在任何地方
                x = random.uniform(0, city_map.width - width)
                y = random.uniform(0, city_map.height - height)
            
            position = Point(x, y)
            test_bounds = Rectangle(x, y, width, height)
            
            # 检查是否与现有组件冲突
            if not city_map.check_collision(test_bounds):
                # 额外检查：水体应该在合适的位置
                if self._is_good_water_location(city_map, test_bounds, water_type):
                    return position
        
        return None
    
    def _is_good_water_location(self, city_map: CityMap, water_bounds: Rectangle, 
                               water_type: str) -> bool:
        """检查是否是好的水体位置"""
        # 检查周围环境
        buffer = 10.0
        expanded_bounds = Rectangle(
            water_bounds.x - buffer,
            water_bounds.y - buffer,
            water_bounds.width + 2 * buffer,
            water_bounds.height + 2 * buffer
        )
        
        nearby_components = city_map.get_components_in_area(expanded_bounds)
        
        # 水体不应该太靠近工业建筑
        for component in nearby_components:
            if (component.type == ComponentType.BUILDING and 
                hasattr(component, 'building_type') and 
                component.building_type == "industrial"):
                return False
        
        # 河流应该有一定的连续性（简化检查）
        if water_type == "river":
            # 检查是否接近地图边缘（河流通常从边缘流入/流出）
            margin = 20.0
            near_edge = (water_bounds.x < margin or 
                        water_bounds.y < margin or
                        water_bounds.x + water_bounds.width > city_map.width - margin or
                        water_bounds.y + water_bounds.height > city_map.height - margin)
            
            if not near_edge:
                return False
        
        return True
    
    def _set_water_properties(self, water: Water, water_type: str):
        """设置水体属性"""
        water.water_type = water_type
        
        if water_type == "lake":
            water.set_property("depth", random.uniform(5.0, 30.0))  # 米
            water.set_property("activities", random.sample([
                "fishing", "boating", "swimming", "kayaking", "bird_watching"
            ], k=random.randint(2, 4)))
            water.set_property("fish_species", random.sample([
                "bass", "trout", "pike", "perch", "carp"
            ], k=random.randint(1, 3)))
            water.set_property("water_quality", random.choice(["excellent", "good", "fair"]))
            
        elif water_type == "river":
            water.set_property("flow_rate", random.uniform(0.5, 3.0))  # m/s
            water.set_property("depth", random.uniform(1.0, 8.0))
            water.set_property("activities", random.sample([
                "fishing", "kayaking", "rafting", "walking_trail"
            ], k=random.randint(1, 3)))
            water.set_property("bridges_needed", random.randint(0, 2))
            water.set_property("seasonal_flooding", random.choice([True, False]))
            
        else:  # pond
            water.set_property("depth", random.uniform(0.5, 3.0))
            water.set_property("activities", random.sample([
                "fishing", "duck_feeding", "meditation", "photography"
            ], k=random.randint(1, 2)))
            water.set_property("wildlife", random.sample([
                "ducks", "frogs", "turtles", "dragonflies"
            ], k=random.randint(1, 3)))
            water.set_property("artificial", random.choice([True, False]))
        
        # 通用属性
        water.set_property("temperature_range", {
            "summer": random.uniform(18, 25),
            "winter": random.uniform(2, 8)
        })
        water.set_property("surrounding_vegetation", random.choice([
            "trees", "reeds", "grass", "mixed"
        ]))
        water.set_property("accessibility", random.choice([True, False]))
    
    def generate_river_system(self, city_map: CityMap) -> List[Water]:
        """生成河流系统"""
        rivers = []
        
        # 创建主河流（横穿或纵穿城市）
        if random.choice([True, False]):
            # 水平主河流
            y = random.uniform(city_map.height * 0.3, city_map.height * 0.7)
            main_river = Water(
                f"main_river_{self.water_counter}",
                Point(0, y - 5),
                city_map.width, 10
            )
            main_river.water_type = "river"
            main_river.set_property("is_main_river", True)
            self._set_water_properties(main_river, "river")
            
            if city_map.add_component(main_river):
                rivers.append(main_river)
                self.water_counter += 1
                
                # 添加支流
                tributary_count = random.randint(1, 3)
                for i in range(tributary_count):
                    trib_x = random.uniform(city_map.width * 0.2, city_map.width * 0.8)
                    trib_length = random.uniform(50, 150)
                    
                    if random.choice([True, False]):  # 向上的支流
                        trib_start_y = y
                        trib_end_y = max(0, y - trib_length)
                    else:  # 向下的支流
                        trib_start_y = y
                        trib_end_y = min(city_map.height, y + trib_length)
                    
                    tributary = Water(
                        f"tributary_{self.water_counter}",
                        Point(trib_x - 2, min(trib_start_y, trib_end_y)),
                        4, abs(trib_end_y - trib_start_y)
                    )
                    tributary.water_type = "river"
                    tributary.set_property("is_tributary", True)
                    self._set_water_properties(tributary, "river")
                    
                    if city_map.add_component(tributary):
                        rivers.append(tributary)
                        self.water_counter += 1
        
        return rivers
