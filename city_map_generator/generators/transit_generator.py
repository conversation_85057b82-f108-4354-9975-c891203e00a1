"""
公共交通生成器

实现地铁、公交等公共交通系统的生成算法
"""

import random
import numpy as np
from typing import List, Tuple, Optional, Dict
from ..components import Point, TransitLine, TransitStation, ComponentType, Road
from ..map import CityMap
from ..config import MapConfig


class TransitConfig:
    """公共交通配置"""
    
    def __init__(self):
        # 地铁配置
        self.subway_probability = 0.6  # 生成地铁的概率
        self.subway_lines_count = (2, 4)  # 地铁线路数量范围
        self.subway_station_spacing = 80.0  # 地铁站间距
        
        # 公交配置
        self.bus_lines_count = (5, 12)  # 公交线路数量范围
        self.bus_station_spacing = 40.0  # 公交站间距
        
        # 有轨电车配置
        self.tram_probability = 0.3  # 生成有轨电车的概率
        self.tram_lines_count = (1, 3)  # 有轨电车线路数量
        
        # 轻轨配置
        self.light_rail_probability = 0.4  # 生成轻轨的概率
        self.light_rail_lines_count = (1, 2)  # 轻轨线路数量


class TransitGenerator:
    """公共交通生成器"""
    
    def __init__(self, config: Optional[TransitConfig] = None):
        self.config = config or TransitConfig()
        self.line_counter = 0
        self.station_counter = 0
    
    def generate_transit_system(self, city_map: CityMap) -> Tuple[List[TransitLine], List[TransitStation]]:
        """生成完整的公共交通系统"""
        lines = []
        stations = []
        
        # 获取现有道路网络作为参考
        roads = city_map.get_components_by_type(ComponentType.ROAD)
        
        # 1. 生成地铁系统
        if random.random() < self.config.subway_probability:
            subway_lines, subway_stations = self._generate_subway_system(city_map, roads)
            lines.extend(subway_lines)
            stations.extend(subway_stations)
        
        # 2. 生成公交系统
        bus_lines, bus_stations = self._generate_bus_system(city_map, roads)
        lines.extend(bus_lines)
        stations.extend(bus_stations)
        
        # 3. 生成有轨电车
        if random.random() < self.config.tram_probability:
            tram_lines, tram_stations = self._generate_tram_system(city_map, roads)
            lines.extend(tram_lines)
            stations.extend(tram_stations)
        
        # 4. 生成轻轨
        if random.random() < self.config.light_rail_probability:
            lr_lines, lr_stations = self._generate_light_rail_system(city_map, roads)
            lines.extend(lr_lines)
            stations.extend(lr_stations)
        
        # 添加到地图
        for line in lines:
            city_map.add_component(line)
        for station in stations:
            city_map.add_component(station)
        
        return lines, stations
    
    def _generate_subway_system(self, city_map: CityMap, roads: List[Road]) -> Tuple[List[TransitLine], List[TransitStation]]:
        """生成地铁系统"""
        lines = []
        stations = []
        
        line_count = random.randint(*self.config.subway_lines_count)
        
        for i in range(line_count):
            # 地铁线路通常是直线或简单曲线，连接城市的主要区域
            if i == 0:
                # 第一条线：横穿城市
                waypoints = self._create_horizontal_line(city_map, 0.3, 0.7)
            elif i == 1:
                # 第二条线：纵穿城市
                waypoints = self._create_vertical_line(city_map, 0.3, 0.7)
            else:
                # 其他线路：连接外围区域
                waypoints = self._create_diagonal_line(city_map)
            
            line = TransitLine(
                f"subway_line_{self.line_counter}",
                waypoints,
                "subway"
            )
            line.line_name = f"地铁{i+1}号线"
            
            # 沿线路添加站点
            line_stations = self._add_stations_along_line(
                line, self.config.subway_station_spacing, "subway_station"
            )
            
            lines.append(line)
            stations.extend(line_stations)
            self.line_counter += 1
        
        return lines, stations
    
    def _generate_bus_system(self, city_map: CityMap, roads: List[Road]) -> Tuple[List[TransitLine], List[TransitStation]]:
        """生成公交系统"""
        lines = []
        stations = []
        
        line_count = random.randint(*self.config.bus_lines_count)
        
        for i in range(line_count):
            # 公交线路沿着现有道路网络
            waypoints = self._create_road_following_line(city_map, roads)
            
            if waypoints:
                line = TransitLine(
                    f"bus_line_{self.line_counter}",
                    waypoints,
                    "bus"
                )
                line.line_name = f"{i+1}路公交"
                
                # 沿线路添加公交站
                line_stations = self._add_stations_along_line(
                    line, self.config.bus_station_spacing, "bus_stop"
                )
                
                lines.append(line)
                stations.extend(line_stations)
                self.line_counter += 1
        
        return lines, stations
    
    def _generate_tram_system(self, city_map: CityMap, roads: List[Road]) -> Tuple[List[TransitLine], List[TransitStation]]:
        """生成有轨电车系统"""
        lines = []
        stations = []
        
        line_count = random.randint(*self.config.tram_lines_count)
        
        for i in range(line_count):
            # 有轨电车通常在市中心区域运行
            waypoints = self._create_city_center_line(city_map)
            
            line = TransitLine(
                f"tram_line_{self.line_counter}",
                waypoints,
                "tram"
            )
            line.line_name = f"有轨电车{i+1}号线"
            
            # 添加电车站
            line_stations = self._add_stations_along_line(
                line, 60.0, "tram_stop"
            )
            
            lines.append(line)
            stations.extend(line_stations)
            self.line_counter += 1
        
        return lines, stations
    
    def _generate_light_rail_system(self, city_map: CityMap, roads: List[Road]) -> Tuple[List[TransitLine], List[TransitStation]]:
        """生成轻轨系统"""
        lines = []
        stations = []
        
        line_count = random.randint(*self.config.light_rail_lines_count)
        
        for i in range(line_count):
            # 轻轨连接市中心和郊区
            waypoints = self._create_suburban_connection_line(city_map)
            
            line = TransitLine(
                f"light_rail_{self.line_counter}",
                waypoints,
                "light_rail"
            )
            line.line_name = f"轻轨{i+1}号线"
            
            # 添加轻轨站
            line_stations = self._add_stations_along_line(
                line, 100.0, "light_rail_station"
            )
            
            lines.append(line)
            stations.extend(line_stations)
            self.line_counter += 1
        
        return lines, stations
    
    def _create_horizontal_line(self, city_map: CityMap, y_start_ratio: float, y_end_ratio: float) -> List[Point]:
        """创建水平线路"""
        y = random.uniform(city_map.height * y_start_ratio, city_map.height * y_end_ratio)
        return [
            Point(city_map.width * 0.05, y),
            Point(city_map.width * 0.95, y)
        ]
    
    def _create_vertical_line(self, city_map: CityMap, x_start_ratio: float, x_end_ratio: float) -> List[Point]:
        """创建垂直线路"""
        x = random.uniform(city_map.width * x_start_ratio, city_map.width * x_end_ratio)
        return [
            Point(x, city_map.height * 0.05),
            Point(x, city_map.height * 0.95)
        ]
    
    def _create_diagonal_line(self, city_map: CityMap) -> List[Point]:
        """创建对角线路"""
        if random.choice([True, False]):
            # 左上到右下
            return [
                Point(city_map.width * 0.1, city_map.height * 0.1),
                Point(city_map.width * 0.9, city_map.height * 0.9)
            ]
        else:
            # 右上到左下
            return [
                Point(city_map.width * 0.9, city_map.height * 0.1),
                Point(city_map.width * 0.1, city_map.height * 0.9)
            ]
    
    def _create_road_following_line(self, city_map: CityMap, roads: List[Road]) -> List[Point]:
        """创建沿道路的线路"""
        if not roads:
            return self._create_horizontal_line(city_map, 0.2, 0.8)
        
        # 选择几条主要道路作为公交线路
        major_roads = [r for r in roads if hasattr(r, 'road_type') and r.road_type in ['highway', 'avenue']]
        if not major_roads:
            major_roads = roads[:3]  # 取前3条道路
        
        waypoints = []
        if major_roads:
            selected_road = random.choice(major_roads)
            waypoints = [selected_road.start_point, selected_road.end_point]
            
            # 添加一些转弯点
            for _ in range(random.randint(1, 3)):
                other_road = random.choice(roads)
                waypoints.append(other_road.start_point)
        
        return waypoints
    
    def _create_city_center_line(self, city_map: CityMap) -> List[Point]:
        """创建市中心线路"""
        center_x = city_map.width / 2
        center_y = city_map.height / 2
        radius = min(city_map.width, city_map.height) * 0.2
        
        # 创建环形或半环形线路
        waypoints = []
        for angle in np.linspace(0, np.pi * 1.5, 6):
            x = center_x + radius * np.cos(angle)
            y = center_y + radius * np.sin(angle)
            waypoints.append(Point(x, y))
        
        return waypoints
    
    def _create_suburban_connection_line(self, city_map: CityMap) -> List[Point]:
        """创建郊区连接线路"""
        # 从市中心到边缘
        center_x = city_map.width / 2
        center_y = city_map.height / 2
        
        # 随机选择一个方向
        angle = random.uniform(0, 2 * np.pi)
        edge_x = center_x + (city_map.width * 0.4) * np.cos(angle)
        edge_y = center_y + (city_map.height * 0.4) * np.sin(angle)
        
        # 确保在边界内
        edge_x = max(city_map.width * 0.1, min(city_map.width * 0.9, edge_x))
        edge_y = max(city_map.height * 0.1, min(city_map.height * 0.9, edge_y))
        
        return [
            Point(center_x, center_y),
            Point(edge_x, edge_y)
        ]
    
    def _add_stations_along_line(self, line: TransitLine, spacing: float, station_type: str) -> List[TransitStation]:
        """沿线路添加站点"""
        stations = []
        
        if len(line.waypoints) < 2:
            return stations
        
        total_length = line.get_total_length()
        station_count = max(2, int(total_length / spacing))
        
        # 沿线路等距离放置站点
        for i in range(station_count):
            ratio = i / (station_count - 1) if station_count > 1 else 0
            position = self._interpolate_along_line(line.waypoints, ratio)
            
            station = TransitStation(
                f"{station_type}_{self.station_counter}",
                position,
                station_type
            )
            station.station_name = f"{line.line_name} - 站点{i+1}"
            station.connect_line(line.id)
            
            # 将站点添加到线路
            line.add_station(position, station.station_name)
            
            stations.append(station)
            self.station_counter += 1
        
        return stations
    
    def _interpolate_along_line(self, waypoints: List[Point], ratio: float) -> Point:
        """沿线路插值获取位置"""
        if len(waypoints) < 2:
            return waypoints[0] if waypoints else Point(0, 0)
        
        if ratio <= 0:
            return waypoints[0]
        if ratio >= 1:
            return waypoints[-1]
        
        # 简化版本：在第一段和最后一段之间线性插值
        start = waypoints[0]
        end = waypoints[-1]
        
        x = start.x + (end.x - start.x) * ratio
        y = start.y + (end.y - start.y) * ratio
        
        return Point(x, y)
