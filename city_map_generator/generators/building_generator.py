"""
建筑物生成器

实现不同类型建筑物的生成算法
"""

import random
import numpy as np
from typing import List, Tuple, Optional, Set
from ..components import Point, Building, ComponentType, Rectangle
from ..map import CityMap
from ..config import MapConfig, BuildingConfig


class BuildingGenerator:
    """建筑物生成器"""
    
    def __init__(self, config: BuildingConfig):
        self.config = config
        self.building_counter = 0
    
    def generate_buildings(self, city_map: CityMap) -> List[Building]:
        """生成建筑物"""
        buildings = []
        
        # 获取可用空间
        available_spaces = self._get_buildable_areas(city_map)
        
        # 计算需要生成的建筑数量
        total_area = city_map.width * city_map.height
        target_building_area = total_area * self.config.building_density
        
        current_area = 0
        attempts = 0
        max_attempts = 1000
        
        while current_area < target_building_area and attempts < max_attempts:
            attempts += 1
            
            # 选择建筑类型
            building_type = self._select_building_type()
            
            # 生成建筑尺寸
            width, height = self._generate_building_size(building_type)
            
            # 寻找合适的位置
            position = self._find_building_position(city_map, width, height, available_spaces)
            
            if position:
                building = Building(
                    f"building_{self.building_counter}",
                    position,
                    width, height,
                    building_type
                )
                
                # 设置建筑属性
                self._set_building_properties(building, building_type)
                
                # 检查是否可以添加
                if city_map.add_component(building):
                    buildings.append(building)
                    current_area += width * height
                    self.building_counter += 1
                    
                    # 更新可用空间
                    self._update_available_spaces(available_spaces, building.get_bounds())
        
        return buildings
    
    def _get_buildable_areas(self, city_map: CityMap) -> List[Rectangle]:
        """获取可建造区域"""
        # 简化版本：整个地图减去道路和其他组件
        buildable_areas = []
        
        # 创建网格来分析可用空间
        grid_size = 20.0
        cols = int(city_map.width / grid_size)
        rows = int(city_map.height / grid_size)
        
        for i in range(cols):
            for j in range(rows):
                x = i * grid_size
                y = j * grid_size
                
                test_area = Rectangle(x, y, grid_size, grid_size)
                
                # 检查是否与道路冲突（保持距离）
                roads = city_map.get_components_by_type(ComponentType.ROAD)
                too_close_to_road = False
                
                for road in roads:
                    road_bounds = road.get_bounds()
                    expanded_bounds = Rectangle(
                        road_bounds.x - self.config.road_setback,
                        road_bounds.y - self.config.road_setback,
                        road_bounds.width + 2 * self.config.road_setback,
                        road_bounds.height + 2 * self.config.road_setback
                    )
                    
                    if test_area.intersects(expanded_bounds):
                        too_close_to_road = True
                        break
                
                if not too_close_to_road:
                    # 检查是否与其他组件冲突
                    if not city_map.check_collision(test_area, {ComponentType.ROAD}):
                        buildable_areas.append(test_area)
        
        return buildable_areas
    
    def _select_building_type(self) -> str:
        """选择建筑类型"""
        rand = random.random()
        
        if rand < self.config.residential_ratio:
            return "residential"
        elif rand < self.config.residential_ratio + self.config.commercial_ratio:
            return "commercial"
        elif rand < (self.config.residential_ratio + self.config.commercial_ratio + 
                    self.config.industrial_ratio):
            return "industrial"
        else:
            return "public"
    
    def _generate_building_size(self, building_type: str) -> Tuple[float, float]:
        """生成建筑尺寸"""
        min_w, min_h = self.config.min_building_size
        max_w, max_h = self.config.max_building_size
        
        # 根据建筑类型调整尺寸范围
        if building_type == "industrial":
            # 工业建筑通常更大
            min_w *= 1.5
            min_h *= 1.5
            max_w *= 1.8
            max_h *= 1.2
        elif building_type == "commercial":
            # 商业建筑可能是高楼
            if random.random() < self.config.skyscraper_probability:
                min_w, min_h = self.config.skyscraper_min_size
                max_w *= 0.8  # 摩天大楼底面积相对较小
                max_h *= 0.8
        elif building_type == "public":
            # 公共建筑尺寸适中但形状可能特殊
            min_w *= 1.2
            min_h *= 1.2
        
        width = random.uniform(min_w, max_w)
        height = random.uniform(min_h, max_h)
        
        return width, height
    
    def _find_building_position(self, city_map: CityMap, width: float, height: float, 
                               available_spaces: List[Rectangle]) -> Optional[Point]:
        """寻找建筑位置"""
        # 过滤出足够大的空间
        suitable_spaces = [space for space in available_spaces 
                          if space.width >= width and space.height >= height]
        
        if not suitable_spaces:
            return None
        
        # 随机选择一个合适的空间
        space = random.choice(suitable_spaces)
        
        # 在空间内随机选择位置
        max_x = space.x + space.width - width
        max_y = space.y + space.height - height
        
        x = random.uniform(space.x, max_x)
        y = random.uniform(space.y, max_y)
        
        position = Point(x, y)
        
        # 最终检查是否与现有组件冲突
        test_bounds = Rectangle(x, y, width, height)
        if city_map.check_collision(test_bounds):
            return None
        
        return position
    
    def _set_building_properties(self, building: Building, building_type: str):
        """设置建筑属性"""
        # 设置楼层数
        if building_type == "residential":
            building.floors = random.randint(1, 8)
        elif building_type == "commercial":
            if random.random() < self.config.skyscraper_probability:
                building.floors = random.randint(10, 50)  # 摩天大楼
            else:
                building.floors = random.randint(1, 12)
        elif building_type == "industrial":
            building.floors = random.randint(1, 4)
        else:  # public
            building.floors = random.randint(1, 6)
        
        # 设置其他属性
        building.set_property("construction_year", random.randint(1950, 2023))
        building.set_property("condition", random.choice(["excellent", "good", "fair", "poor"]))
        
        if building_type == "residential":
            building.set_property("units", building.floors * random.randint(2, 8))
            building.set_property("parking_spaces", building.get_property("units") * random.uniform(0.5, 1.5))
        elif building_type == "commercial":
            building.set_property("business_type", random.choice([
                "retail", "office", "restaurant", "hotel", "shopping_center"
            ]))
            building.set_property("parking_spaces", building.floors * random.randint(10, 50))
        elif building_type == "industrial":
            building.set_property("industry_type", random.choice([
                "manufacturing", "warehouse", "logistics", "processing"
            ]))
            building.set_property("loading_docks", random.randint(1, 8))
        else:  # public
            building.set_property("facility_type", random.choice([
                "school", "hospital", "library", "government", "fire_station", "police_station"
            ]))
            building.set_property("capacity", building.floors * random.randint(50, 200))
    
    def _update_available_spaces(self, available_spaces: List[Rectangle], occupied_bounds: Rectangle):
        """更新可用空间（移除被占用的区域）"""
        # 简化版本：移除与占用区域相交的空间
        spaces_to_remove = []
        
        for i, space in enumerate(available_spaces):
            if space.intersects(occupied_bounds):
                spaces_to_remove.append(i)
        
        # 从后往前删除，避免索引问题
        for i in reversed(spaces_to_remove):
            available_spaces.pop(i)
    
    def generate_building_cluster(self, city_map: CityMap, center: Point, 
                                 cluster_type: str, cluster_size: int = 5) -> List[Building]:
        """生成建筑群（如住宅小区、商业区等）"""
        buildings = []
        
        cluster_radius = 50.0
        
        for _ in range(cluster_size):
            # 在中心点周围生成位置
            angle = random.uniform(0, 2 * np.pi)
            distance = random.uniform(0, cluster_radius)
            
            x = center.x + distance * np.cos(angle)
            y = center.y + distance * np.sin(angle)
            
            # 确保在地图范围内
            x = max(0, min(city_map.width - 20, x))
            y = max(0, min(city_map.height - 20, y))
            
            position = Point(x, y)
            width, height = self._generate_building_size(cluster_type)
            
            # 检查位置是否可用
            test_bounds = Rectangle(x, y, width, height)
            if not city_map.check_collision(test_bounds):
                building = Building(
                    f"cluster_{cluster_type}_{self.building_counter}",
                    position, width, height, cluster_type
                )
                
                self._set_building_properties(building, cluster_type)
                
                if city_map.add_component(building):
                    buildings.append(building)
                    self.building_counter += 1
        
        return buildings
