"""
建筑物生成器

实现不同类型建筑物的生成算法
"""

import random
import numpy as np
from typing import List, Tuple, Optional, Set
from ..components import Point, Building, ComponentType, Rectangle, Road
from ..map import CityMap
from ..config import MapConfig, BuildingConfig
from ..zoning import ZoningSystem


class BuildingGenerator:
    """建筑物生成器"""
    
    def __init__(self, config: BuildingConfig, zoning_system: Optional[ZoningSystem] = None):
        self.config = config
        self.building_counter = 0
        self.zoning_system = zoning_system
    
    def generate_buildings(self, city_map: CityMap) -> List[Building]:
        """生成建筑物 - 优先沿道路分布"""
        buildings = []

        # 获取道路网络
        roads = city_map.get_components_by_type(ComponentType.ROAD)

        if not roads:
            # 如果没有道路，使用原来的方法
            return self._generate_buildings_without_roads(city_map)

        # 沿道路生成建筑
        road_buildings = self._generate_buildings_along_roads(city_map, roads)
        buildings.extend(road_buildings)

        # 在剩余空间填充建筑
        fill_buildings = self._fill_remaining_spaces(city_map, roads)
        buildings.extend(fill_buildings)

        return buildings

    def _generate_buildings_along_roads(self, city_map: CityMap, roads: List[Road]) -> List[Building]:
        """沿道路生成建筑物"""
        buildings = []

        for road in roads:
            # 在道路两侧生成建筑
            road_buildings = self._generate_buildings_beside_road(city_map, road)
            buildings.extend(road_buildings)

        return buildings

    def _generate_buildings_beside_road(self, city_map: CityMap, road: Road) -> List[Building]:
        """在道路两侧生成建筑"""
        buildings = []

        # 计算道路方向向量
        dx = road.end_point.x - road.start_point.x
        dy = road.end_point.y - road.start_point.y
        road_length = np.sqrt(dx*dx + dy*dy)

        if road_length < 10:  # 道路太短，跳过
            return buildings

        # 标准化方向向量
        dx /= road_length
        dy /= road_length

        # 垂直向量（道路两侧）
        perp_x = -dy
        perp_y = dx

        # 沿道路每隔一定距离放置建筑
        building_spacing = 25.0  # 建筑间距
        setback_distance = self.config.road_setback + road.width/2 + 5.0  # 距离道路的距离

        num_positions = int(road_length / building_spacing)

        for i in range(num_positions):
            # 沿道路的位置
            t = (i + 0.5) / num_positions  # 避免在道路端点
            road_x = road.start_point.x + t * dx * road_length
            road_y = road.start_point.y + t * dy * road_length

            # 在道路两侧尝试放置建筑
            for side in [-1, 1]:  # 左侧和右侧
                if random.random() > 0.7:  # 70%概率放置建筑
                    continue

                # 计算建筑位置
                building_x = road_x + side * perp_x * setback_distance
                building_y = road_y + side * perp_y * setback_distance

                # 确保在地图范围内
                if (building_x < 0 or building_x >= city_map.width - 20 or
                    building_y < 0 or building_y >= city_map.height - 20):
                    continue

                # 根据道路类型和分区选择建筑类型
                building_type = self._select_building_type_by_location(road, Point(building_x, building_y))
                width, height = self._generate_building_size(building_type)

                position = Point(building_x, building_y)
                test_bounds = Rectangle(building_x, building_y, width, height)

                # 检查碰撞
                if not city_map.check_collision(test_bounds):
                    building = Building(
                        f"building_{self.building_counter}",
                        position, width, height, building_type
                    )

                    self._set_building_properties(building, building_type)

                    if city_map.add_component(building):
                        buildings.append(building)
                        self.building_counter += 1

        return buildings

    def _select_building_type_by_road(self, road: Road) -> str:
        """根据道路类型选择建筑类型"""
        if hasattr(road, 'road_type'):
            if road.road_type == 'highway':
                # 主干道旁更多商业建筑
                return random.choices(
                    ['commercial', 'residential', 'public', 'industrial'],
                    weights=[0.5, 0.3, 0.15, 0.05]
                )[0]
            elif road.road_type == 'avenue':
                # 次干道混合用途
                return random.choices(
                    ['residential', 'commercial', 'public', 'industrial'],
                    weights=[0.4, 0.4, 0.15, 0.05]
                )[0]
            else:  # street
                # 小路主要是住宅
                return random.choices(
                    ['residential', 'commercial', 'public', 'industrial'],
                    weights=[0.7, 0.2, 0.08, 0.02]
                )[0]

        return self._select_building_type()

    def _select_building_type_by_location(self, road: Road, position: Point) -> str:
        """根据位置和道路类型选择建筑类型（考虑分区）"""
        # 如果有分区系统，优先使用分区规则
        if self.zoning_system:
            requirements = self.zoning_system.get_building_requirements(position)
            allowed_types = requirements['allowed_types']

            if allowed_types:
                # 在允许的类型中随机选择
                return random.choice(allowed_types)

        # 否则使用道路类型选择
        return self._select_building_type_by_road(road)

    def _fill_remaining_spaces(self, city_map: CityMap, roads: List[Road]) -> List[Building]:
        """在剩余空间填充建筑"""
        buildings = []

        # 计算当前建筑密度
        current_buildings = city_map.get_components_by_type(ComponentType.BUILDING)
        current_area = sum(b.width * b.height for b in current_buildings)
        total_area = city_map.width * city_map.height
        current_density = current_area / total_area

        if current_density >= self.config.building_density:
            return buildings

        # 需要填充的面积
        target_area = total_area * self.config.building_density
        remaining_area = target_area - current_area

        attempts = 0
        max_attempts = 500

        while remaining_area > 0 and attempts < max_attempts:
            attempts += 1

            building_type = self._select_building_type()
            width, height = self._generate_building_size(building_type)

            # 随机选择位置，但避免太靠近道路（已经有建筑了）
            position = self._find_interior_position(city_map, width, height, roads)

            if position:
                building = Building(
                    f"building_{self.building_counter}",
                    position, width, height, building_type
                )

                self._set_building_properties(building, building_type)

                if city_map.add_component(building):
                    buildings.append(building)
                    remaining_area -= width * height
                    self.building_counter += 1

        return buildings

    def _find_interior_position(self, city_map: CityMap, width: float, height: float, roads: List[Road]) -> Optional[Point]:
        """寻找内部位置（远离道路）"""
        max_attempts = 50

        for _ in range(max_attempts):
            x = random.uniform(0, city_map.width - width)
            y = random.uniform(0, city_map.height - height)

            position = Point(x, y)
            test_bounds = Rectangle(x, y, width, height)

            # 检查是否距离道路足够远
            min_distance_to_road = float('inf')
            for road in roads:
                distance = self._distance_point_to_road(position, road)
                min_distance_to_road = min(min_distance_to_road, distance)

            # 确保距离道路至少30米（避免与沿路建筑重叠）
            if min_distance_to_road > 30.0 and not city_map.check_collision(test_bounds):
                return position

        return None

    def _distance_point_to_road(self, point: Point, road: Road) -> float:
        """计算点到道路的距离"""
        return self._distance_to_line(point, road.start_point, road.end_point)

    def _distance_to_line(self, point: Point, line_start: Point, line_end: Point) -> float:
        """计算点到线段的距离"""
        A = point.x - line_start.x
        B = point.y - line_start.y
        C = line_end.x - line_start.x
        D = line_end.y - line_start.y

        dot = A * C + B * D
        len_sq = C * C + D * D

        if len_sq == 0:
            return point.distance_to(line_start)

        param = dot / len_sq

        if param < 0:
            xx = line_start.x
            yy = line_start.y
        elif param > 1:
            xx = line_end.x
            yy = line_end.y
        else:
            xx = line_start.x + param * C
            yy = line_start.y + param * D

        dx = point.x - xx
        dy = point.y - yy
        return np.sqrt(dx * dx + dy * dy)

    def _generate_buildings_without_roads(self, city_map: CityMap) -> List[Building]:
        """没有道路时的建筑生成方法（原方法）"""
        buildings = []

        # 获取可用空间
        available_spaces = self._get_buildable_areas(city_map)

        # 计算需要生成的建筑数量
        total_area = city_map.width * city_map.height
        target_building_area = total_area * self.config.building_density

        current_area = 0
        attempts = 0
        max_attempts = 1000

        while current_area < target_building_area and attempts < max_attempts:
            attempts += 1

            # 选择建筑类型
            building_type = self._select_building_type()

            # 生成建筑尺寸
            width, height = self._generate_building_size(building_type)

            # 寻找合适的位置
            position = self._find_building_position(city_map, width, height, available_spaces)

            if position:
                building = Building(
                    f"building_{self.building_counter}",
                    position,
                    width, height,
                    building_type
                )

                # 设置建筑属性
                self._set_building_properties(building, building_type)

                # 检查是否可以添加
                if city_map.add_component(building):
                    buildings.append(building)
                    current_area += width * height
                    self.building_counter += 1

                    # 更新可用空间
                    self._update_available_spaces(available_spaces, building.get_bounds())

        return buildings
    
    def _get_buildable_areas(self, city_map: CityMap) -> List[Rectangle]:
        """获取可建造区域"""
        # 简化版本：整个地图减去道路和其他组件
        buildable_areas = []
        
        # 创建网格来分析可用空间
        grid_size = 20.0
        cols = int(city_map.width / grid_size)
        rows = int(city_map.height / grid_size)
        
        for i in range(cols):
            for j in range(rows):
                x = i * grid_size
                y = j * grid_size
                
                test_area = Rectangle(x, y, grid_size, grid_size)
                
                # 检查是否与道路冲突（保持距离）
                roads = city_map.get_components_by_type(ComponentType.ROAD)
                too_close_to_road = False
                
                for road in roads:
                    road_bounds = road.get_bounds()
                    expanded_bounds = Rectangle(
                        road_bounds.x - self.config.road_setback,
                        road_bounds.y - self.config.road_setback,
                        road_bounds.width + 2 * self.config.road_setback,
                        road_bounds.height + 2 * self.config.road_setback
                    )
                    
                    if test_area.intersects(expanded_bounds):
                        too_close_to_road = True
                        break
                
                if not too_close_to_road:
                    # 检查是否与其他组件冲突
                    if not city_map.check_collision(test_area, {ComponentType.ROAD}):
                        buildable_areas.append(test_area)
        
        return buildable_areas
    
    def _select_building_type(self) -> str:
        """选择建筑类型"""
        rand = random.random()
        
        if rand < self.config.residential_ratio:
            return "residential"
        elif rand < self.config.residential_ratio + self.config.commercial_ratio:
            return "commercial"
        elif rand < (self.config.residential_ratio + self.config.commercial_ratio + 
                    self.config.industrial_ratio):
            return "industrial"
        else:
            return "public"
    
    def _generate_building_size(self, building_type: str) -> Tuple[float, float]:
        """生成建筑尺寸"""
        min_w, min_h = self.config.min_building_size
        max_w, max_h = self.config.max_building_size
        
        # 根据建筑类型调整尺寸范围
        if building_type == "industrial":
            # 工业建筑通常更大
            min_w *= 1.5
            min_h *= 1.5
            max_w *= 1.8
            max_h *= 1.2
        elif building_type == "commercial":
            # 商业建筑可能是高楼
            if random.random() < self.config.skyscraper_probability:
                min_w, min_h = self.config.skyscraper_min_size
                max_w *= 0.8  # 摩天大楼底面积相对较小
                max_h *= 0.8
        elif building_type == "public":
            # 公共建筑尺寸适中但形状可能特殊
            min_w *= 1.2
            min_h *= 1.2
        
        width = random.uniform(min_w, max_w)
        height = random.uniform(min_h, max_h)
        
        return width, height
    
    def _find_building_position(self, city_map: CityMap, width: float, height: float, 
                               available_spaces: List[Rectangle]) -> Optional[Point]:
        """寻找建筑位置"""
        # 过滤出足够大的空间
        suitable_spaces = [space for space in available_spaces 
                          if space.width >= width and space.height >= height]
        
        if not suitable_spaces:
            return None
        
        # 随机选择一个合适的空间
        space = random.choice(suitable_spaces)
        
        # 在空间内随机选择位置
        max_x = space.x + space.width - width
        max_y = space.y + space.height - height
        
        x = random.uniform(space.x, max_x)
        y = random.uniform(space.y, max_y)
        
        position = Point(x, y)
        
        # 最终检查是否与现有组件冲突
        test_bounds = Rectangle(x, y, width, height)
        if city_map.check_collision(test_bounds):
            return None
        
        return position
    
    def _set_building_properties(self, building: Building, building_type: str):
        """设置建筑属性（考虑分区限制）"""
        # 获取分区限制
        height_limit = 50  # 默认高度限制
        if self.zoning_system:
            requirements = self.zoning_system.get_building_requirements(building.position)
            height_limit = requirements.get('height_limit', 50)

        # 设置楼层数（受分区限制）
        if building_type == "residential":
            max_floors = min(8, height_limit)
            building.floors = random.randint(1, max_floors)
        elif building_type == "commercial":
            if random.random() < self.config.skyscraper_probability and height_limit > 15:
                building.floors = random.randint(10, min(50, height_limit))  # 摩天大楼
            else:
                max_floors = min(12, height_limit)
                building.floors = random.randint(1, max_floors)
        elif building_type == "industrial":
            max_floors = min(4, height_limit)
            building.floors = random.randint(1, max_floors)
        else:  # public
            max_floors = min(6, height_limit)
            building.floors = random.randint(1, max_floors)
        
        # 设置其他属性
        building.set_property("construction_year", random.randint(1950, 2023))
        building.set_property("condition", random.choice(["excellent", "good", "fair", "poor"]))
        
        if building_type == "residential":
            building.set_property("units", building.floors * random.randint(2, 8))
            building.set_property("parking_spaces", building.get_property("units") * random.uniform(0.5, 1.5))
        elif building_type == "commercial":
            building.set_property("business_type", random.choice([
                "retail", "office", "restaurant", "hotel", "shopping_center"
            ]))
            building.set_property("parking_spaces", building.floors * random.randint(10, 50))
        elif building_type == "industrial":
            building.set_property("industry_type", random.choice([
                "manufacturing", "warehouse", "logistics", "processing"
            ]))
            building.set_property("loading_docks", random.randint(1, 8))
        else:  # public
            building.set_property("facility_type", random.choice([
                "school", "hospital", "library", "government", "fire_station", "police_station"
            ]))
            building.set_property("capacity", building.floors * random.randint(50, 200))
    
    def _update_available_spaces(self, available_spaces: List[Rectangle], occupied_bounds: Rectangle):
        """更新可用空间（移除被占用的区域）"""
        # 简化版本：移除与占用区域相交的空间
        spaces_to_remove = []
        
        for i, space in enumerate(available_spaces):
            if space.intersects(occupied_bounds):
                spaces_to_remove.append(i)
        
        # 从后往前删除，避免索引问题
        for i in reversed(spaces_to_remove):
            available_spaces.pop(i)
    
    def generate_building_cluster(self, city_map: CityMap, center: Point, 
                                 cluster_type: str, cluster_size: int = 5) -> List[Building]:
        """生成建筑群（如住宅小区、商业区等）"""
        buildings = []
        
        cluster_radius = 50.0
        
        for _ in range(cluster_size):
            # 在中心点周围生成位置
            angle = random.uniform(0, 2 * np.pi)
            distance = random.uniform(0, cluster_radius)
            
            x = center.x + distance * np.cos(angle)
            y = center.y + distance * np.sin(angle)
            
            # 确保在地图范围内
            x = max(0, min(city_map.width - 20, x))
            y = max(0, min(city_map.height - 20, y))
            
            position = Point(x, y)
            width, height = self._generate_building_size(cluster_type)
            
            # 检查位置是否可用
            test_bounds = Rectangle(x, y, width, height)
            if not city_map.check_collision(test_bounds):
                building = Building(
                    f"cluster_{cluster_type}_{self.building_counter}",
                    position, width, height, cluster_type
                )
                
                self._set_building_properties(building, cluster_type)
                
                if city_map.add_component(building):
                    buildings.append(building)
                    self.building_counter += 1
        
        return buildings
