"""
道路生成器

实现道路网络的生成算法
"""

import random
import numpy as np
from typing import List, Tuple, Optional, Set
from ..components import Point, Road, ComponentType
from ..map import CityMap
from ..config import MapConfig, RoadConfig


class RoadGenerator:
    """道路生成器"""
    
    def __init__(self, config: RoadConfig):
        self.config = config
        self.road_counter = 0
    
    def generate_roads(self, city_map: CityMap) -> List[Road]:
        """生成完整的道路网络 - 有机和真实的道路系统"""
        roads = []

        # 1. 生成主要道路骨架（部分曲线）
        main_roads = self._generate_organic_main_roads(city_map)
        roads.extend(main_roads)

        # 2. 生成次干道网络（混合直线和曲线）
        secondary_roads = self._generate_mixed_secondary_roads(city_map)
        roads.extend(secondary_roads)

        # 3. 生成有机街道网络
        street_network = self._generate_organic_street_network(city_map)
        roads.extend(street_network)

        # 4. 添加连接道路和环路
        connector_roads = self._generate_connector_roads(city_map, roads)
        roads.extend(connector_roads)

        # 5. 添加一些随机的有机道路
        organic_roads = self._generate_random_organic_roads(city_map, roads)
        roads.extend(organic_roads)

        # 6. 添加道路到地图
        for road in roads:
            city_map.add_component(road)

        return roads

    def _generate_organic_main_roads(self, city_map: CityMap) -> List[Road]:
        """生成有机的主干道"""
        roads = []

        # 垂直主干道 - 添加轻微曲线
        vertical_spacing = city_map.width / (self.config.main_roads_count + 1)
        for i in range(self.config.main_roads_count):
            base_x = (i + 1) * vertical_spacing

            # 添加轻微的随机偏移
            x_variation = random.uniform(-10, 10)
            x = base_x + x_variation

            start = Point(x, 0)
            end = Point(x + random.uniform(-15, 15), city_map.height)

            # 30%概率生成曲线道路，70%概率生成有机道路
            if random.random() < 0.3:
                road = Road.create_curved_road(
                    f"main_v_{self.road_counter}",
                    start, end,
                    curvature=0.1,
                    segments=6,
                    width=self.config.main_road_width
                )
            else:
                road = Road.create_organic_road(
                    f"main_v_{self.road_counter}",
                    start, end,
                    noise_level=0.05,
                    segments=8,
                    width=self.config.main_road_width
                )

            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1

        # 水平主干道 - 添加轻微曲线
        horizontal_spacing = city_map.height / (self.config.main_roads_count + 1)
        for i in range(self.config.main_roads_count):
            base_y = (i + 1) * horizontal_spacing

            # 添加轻微的随机偏移
            y_variation = random.uniform(-10, 10)
            y = base_y + y_variation

            start = Point(0, y)
            end = Point(city_map.width, y + random.uniform(-15, 15))

            # 30%概率生成曲线道路，70%概率生成有机道路
            if random.random() < 0.3:
                road = Road.create_curved_road(
                    f"main_h_{self.road_counter}",
                    start, end,
                    curvature=0.1,
                    segments=6,
                    width=self.config.main_road_width
                )
            else:
                road = Road.create_organic_road(
                    f"main_h_{self.road_counter}",
                    start, end,
                    noise_level=0.05,
                    segments=8,
                    width=self.config.main_road_width
                )

            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1

        return roads

    def _generate_mixed_secondary_roads(self, city_map: CityMap) -> List[Road]:
        """生成混合类型的次干道"""
        roads = []

        # 计算网格间距
        grid_spacing = self.config.secondary_road_spacing

        # 垂直次干道
        x = grid_spacing / 2
        while x < city_map.width:
            if not self._too_close_to_existing_vertical_road(x, city_map):
                # 添加随机偏移
                actual_x = x + random.uniform(-20, 20)
                start = Point(actual_x, random.uniform(0, 20))
                end = Point(actual_x + random.uniform(-25, 25),
                           city_map.height - random.uniform(0, 20))

                # 选择道路类型
                road_type_choice = random.random()
                if road_type_choice < 0.4:
                    # 40% 直线道路
                    road = Road.create_straight_road(
                        f"sec_v_{self.road_counter}",
                        start, end,
                        self.config.secondary_road_width
                    )
                elif road_type_choice < 0.7:
                    # 30% 曲线道路
                    road = Road.create_curved_road(
                        f"sec_v_{self.road_counter}",
                        start, end,
                        curvature=0.15,
                        segments=5,
                        width=self.config.secondary_road_width
                    )
                else:
                    # 30% 有机道路
                    road = Road.create_organic_road(
                        f"sec_v_{self.road_counter}",
                        start, end,
                        noise_level=0.08,
                        segments=6,
                        width=self.config.secondary_road_width
                    )

                road.road_type = "avenue"
                roads.append(road)
                self.road_counter += 1

            x += grid_spacing

        # 水平次干道
        y = grid_spacing / 2
        while y < city_map.height:
            if not self._too_close_to_existing_horizontal_road(y, city_map):
                # 添加随机偏移
                actual_y = y + random.uniform(-20, 20)
                start = Point(random.uniform(0, 20), actual_y)
                end = Point(city_map.width - random.uniform(0, 20),
                           actual_y + random.uniform(-25, 25))

                # 选择道路类型
                road_type_choice = random.random()
                if road_type_choice < 0.4:
                    # 40% 直线道路
                    road = Road.create_straight_road(
                        f"sec_h_{self.road_counter}",
                        start, end,
                        self.config.secondary_road_width
                    )
                elif road_type_choice < 0.7:
                    # 30% 曲线道路
                    road = Road.create_curved_road(
                        f"sec_h_{self.road_counter}",
                        start, end,
                        curvature=0.15,
                        segments=5,
                        width=self.config.secondary_road_width
                    )
                else:
                    # 30% 有机道路
                    road = Road.create_organic_road(
                        f"sec_h_{self.road_counter}",
                        start, end,
                        noise_level=0.08,
                        segments=6,
                        width=self.config.secondary_road_width
                    )

                road.road_type = "avenue"
                roads.append(road)
                self.road_counter += 1

            y += grid_spacing

        return roads

    def _generate_main_road_grid(self, city_map: CityMap) -> List[Road]:
        """生成主干道网格系统"""
        roads = []

        # 垂直主干道 - 更密集的间距
        vertical_spacing = city_map.width / (self.config.main_roads_count + 1)
        for i in range(self.config.main_roads_count):
            x = (i + 1) * vertical_spacing
            start = Point(x, 0)
            end = Point(x, city_map.height)

            road = Road(
                f"main_v_{self.road_counter}",
                start, end,
                self.config.main_road_width
            )
            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1

        # 水平主干道 - 更密集的间距
        horizontal_spacing = city_map.height / (self.config.main_roads_count + 1)
        for i in range(self.config.main_roads_count):
            y = (i + 1) * horizontal_spacing
            start = Point(0, y)
            end = Point(city_map.width, y)

            road = Road(
                f"main_h_{self.road_counter}",
                start, end,
                self.config.main_road_width
            )
            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1

        return roads

    def _generate_secondary_road_grid(self, city_map: CityMap) -> List[Road]:
        """生成次干道网格系统"""
        roads = []

        # 计算网格间距
        grid_spacing = self.config.secondary_road_spacing

        # 垂直次干道
        x = grid_spacing / 2
        while x < city_map.width:
            # 检查是否与主干道重叠
            if not self._too_close_to_existing_vertical_road(x, city_map):
                start = Point(x, 0)
                end = Point(x, city_map.height)

                road = Road(
                    f"sec_v_{self.road_counter}",
                    start, end,
                    self.config.secondary_road_width
                )
                road.road_type = "avenue"
                roads.append(road)
                self.road_counter += 1

            x += grid_spacing

        # 水平次干道
        y = grid_spacing / 2
        while y < city_map.height:
            # 检查是否与主干道重叠
            if not self._too_close_to_existing_horizontal_road(y, city_map):
                start = Point(0, y)
                end = Point(city_map.width, y)

                road = Road(
                    f"sec_h_{self.road_counter}",
                    start, end,
                    self.config.secondary_road_width
                )
                road.road_type = "avenue"
                roads.append(road)
                self.road_counter += 1

            y += grid_spacing

        return roads

    def _generate_street_network(self, city_map: CityMap) -> List[Road]:
        """生成密集的街道网络"""
        roads = []

        # 街道网格间距
        street_spacing = self.config.street_spacing

        # 垂直街道
        x = street_spacing / 3
        while x < city_map.width:
            if not self._too_close_to_existing_vertical_road(x, city_map):
                start = Point(x, 0)
                end = Point(x, city_map.height)

                road = Road(
                    f"street_v_{self.road_counter}",
                    start, end,
                    self.config.street_width
                )
                road.road_type = "street"
                roads.append(road)
                self.road_counter += 1

            x += street_spacing

        # 水平街道
        y = street_spacing / 3
        while y < city_map.height:
            if not self._too_close_to_existing_horizontal_road(y, city_map):
                start = Point(0, y)
                end = Point(city_map.width, y)

                road = Road(
                    f"street_h_{self.road_counter}",
                    start, end,
                    self.config.street_width
                )
                road.road_type = "street"
                roads.append(road)
                self.road_counter += 1

            y += street_spacing

        return roads

    def _generate_organic_street_network(self, city_map: CityMap) -> List[Road]:
        """生成有机的街道网络"""
        roads = []

        # 街道网格间距
        street_spacing = self.config.street_spacing

        # 生成更少但更有机的街道
        street_count = int(self.config.street_density * 20)  # 减少街道数量，提高质量

        for _ in range(street_count):
            # 随机选择起点
            start_x = random.uniform(0, city_map.width)
            start_y = random.uniform(0, city_map.height)
            start = Point(start_x, start_y)

            # 随机选择方向和长度
            angle = random.uniform(0, 2 * np.pi)
            length = random.uniform(30, 120)

            end_x = start_x + length * np.cos(angle)
            end_y = start_y + length * np.sin(angle)

            # 确保终点在地图范围内
            end_x = max(0, min(city_map.width, end_x))
            end_y = max(0, min(city_map.height, end_y))
            end = Point(end_x, end_y)

            # 生成有机道路
            road = Road.create_organic_road(
                f"street_{self.road_counter}",
                start, end,
                noise_level=0.12,
                segments=random.randint(4, 8),
                width=self.config.street_width
            )
            road.road_type = "street"
            roads.append(road)
            self.road_counter += 1

        return roads

    def _generate_connector_roads(self, city_map: CityMap, existing_roads: List[Road]) -> List[Road]:
        """生成连接道路和环路"""
        roads = []

        # 生成一些环形道路
        ring_roads = self._generate_ring_roads(city_map)
        roads.extend(ring_roads)

        # 生成径向道路
        radial_roads = self._generate_radial_roads(city_map)
        roads.extend(radial_roads)

        return roads

    def _generate_ring_roads(self, city_map: CityMap) -> List[Road]:
        """生成环形道路"""
        roads = []

        center_x = city_map.width / 2
        center_y = city_map.height / 2

        # 生成2-3个环路
        ring_count = random.randint(2, 3)

        for i in range(ring_count):
            radius = (i + 1) * min(city_map.width, city_map.height) / (ring_count + 2) / 2

            # 生成环形路径点
            waypoints = []
            segments = 12

            for j in range(segments):
                angle = 2 * np.pi * j / segments

                # 添加噪声使环形不规则
                noise_radius = radius * (1 + random.uniform(-0.2, 0.2))
                noise_angle = angle + random.uniform(-0.1, 0.1)

                x = center_x + noise_radius * np.cos(noise_angle)
                y = center_y + noise_radius * np.sin(noise_angle)

                # 确保在地图范围内
                x = max(20, min(city_map.width - 20, x))
                y = max(20, min(city_map.height - 20, y))

                waypoints.append(Point(x, y))

            # 闭合环路
            waypoints.append(waypoints[0])

            road = Road(f"ring_{self.road_counter}", waypoints, self.config.secondary_road_width)
            road.road_type = "avenue"
            roads.append(road)
            self.road_counter += 1

        return roads

    def _generate_radial_roads(self, city_map: CityMap) -> List[Road]:
        """生成径向道路"""
        roads = []

        center_x = city_map.width / 2
        center_y = city_map.height / 2
        center = Point(center_x, center_y)

        # 生成4-6条径向道路
        radial_count = random.randint(4, 6)

        for i in range(radial_count):
            angle = 2 * np.pi * i / radial_count + random.uniform(-0.3, 0.3)

            # 计算终点
            max_radius = min(city_map.width, city_map.height) / 2 * 0.8
            radius = random.uniform(max_radius * 0.6, max_radius)

            end_x = center_x + radius * np.cos(angle)
            end_y = center_y + radius * np.sin(angle)

            # 确保在地图范围内
            end_x = max(0, min(city_map.width, end_x))
            end_y = max(0, min(city_map.height, end_y))
            end = Point(end_x, end_y)

            # 生成曲线径向道路
            road = Road.create_curved_road(
                f"radial_{self.road_counter}",
                center, end,
                curvature=0.1,
                segments=6,
                width=self.config.secondary_road_width
            )
            road.road_type = "avenue"
            roads.append(road)
            self.road_counter += 1

        return roads

    def _generate_random_organic_roads(self, city_map: CityMap, existing_roads: List[Road]) -> List[Road]:
        """生成随机的有机道路"""
        roads = []

        # 生成一些完全随机的有机道路
        organic_count = random.randint(5, 10)

        for _ in range(organic_count):
            # 随机起点和终点
            start = Point(
                random.uniform(city_map.width * 0.1, city_map.width * 0.9),
                random.uniform(city_map.height * 0.1, city_map.height * 0.9)
            )

            # 随机方向和距离
            angle = random.uniform(0, 2 * np.pi)
            distance = random.uniform(40, 100)

            end_x = start.x + distance * np.cos(angle)
            end_y = start.y + distance * np.sin(angle)

            # 确保在地图范围内
            end_x = max(0, min(city_map.width, end_x))
            end_y = max(0, min(city_map.height, end_y))
            end = Point(end_x, end_y)

            # 生成高度有机的道路
            road = Road.create_organic_road(
                f"organic_{self.road_counter}",
                start, end,
                noise_level=0.15,
                segments=random.randint(6, 10),
                width=self.config.street_width
            )
            road.road_type = "street"
            roads.append(road)
            self.road_counter += 1

        return roads

    def _ensure_road_connectivity(self, existing_roads: List[Road], city_map: CityMap) -> List[Road]:
        """确保道路连接性，添加连接道路"""
        connector_roads = []

        # 添加一些对角线连接道路
        diagonal_count = self.config.connector_roads_count

        for i in range(diagonal_count):
            # 随机选择起点和终点
            start_x = random.uniform(city_map.width * 0.1, city_map.width * 0.9)
            start_y = random.uniform(city_map.height * 0.1, city_map.height * 0.9)

            # 生成对角线方向
            angle = random.uniform(0, 2 * np.pi)
            length = random.uniform(50, 150)

            end_x = start_x + length * np.cos(angle)
            end_y = start_y + length * np.sin(angle)

            # 确保终点在地图范围内
            end_x = max(0, min(city_map.width, end_x))
            end_y = max(0, min(city_map.height, end_y))

            start = Point(start_x, start_y)
            end = Point(end_x, end_y)

            road = Road(
                f"connector_{self.road_counter}",
                start, end,
                self.config.street_width
            )
            road.road_type = "street"
            connector_roads.append(road)
            self.road_counter += 1

        return connector_roads

    def _too_close_to_existing_vertical_road(self, x: float, city_map: CityMap) -> bool:
        """检查垂直道路是否太接近现有道路"""
        min_distance = 15.0  # 最小距离

        # 检查主干道位置
        main_road_spacing = city_map.width / (self.config.main_roads_count + 1)
        for i in range(self.config.main_roads_count):
            main_x = (i + 1) * main_road_spacing
            if abs(x - main_x) < min_distance:
                return True

        return False

    def _too_close_to_existing_horizontal_road(self, y: float, city_map: CityMap) -> bool:
        """检查水平道路是否太接近现有道路"""
        min_distance = 15.0  # 最小距离

        # 检查主干道位置
        main_road_spacing = city_map.height / (self.config.main_roads_count + 1)
        for i in range(self.config.main_roads_count):
            main_y = (i + 1) * main_road_spacing
            if abs(y - main_y) < min_distance:
                return True

        return False

    def _generate_main_roads(self, city_map: CityMap) -> List[Road]:
        """生成主干道"""
        roads = []
        
        # 垂直主干道
        vertical_count = max(1, self.config.main_roads_count // 2)
        for i in range(vertical_count):
            x = (i + 1) * city_map.width / (vertical_count + 1)
            start = Point(x, 0)
            end = Point(x, city_map.height)
            
            road = Road(
                f"main_v_{self.road_counter}",
                start, end,
                self.config.main_road_width
            )
            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1
        
        # 水平主干道
        horizontal_count = self.config.main_roads_count - vertical_count
        for i in range(horizontal_count):
            y = (i + 1) * city_map.height / (horizontal_count + 1)
            start = Point(0, y)
            end = Point(city_map.width, y)
            
            road = Road(
                f"main_h_{self.road_counter}",
                start, end,
                self.config.main_road_width
            )
            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1
        
        return roads
    
    def _generate_secondary_roads(self, city_map: CityMap, main_roads: List[Road]) -> List[Road]:
        """生成次干道"""
        roads = []
        
        # 在主干道之间生成次干道
        vertical_main = [r for r in main_roads if abs(r.start_point.y - 0) < 1]
        horizontal_main = [r for r in main_roads if abs(r.start_point.x - 0) < 1]
        
        # 垂直次干道
        if len(vertical_main) >= 2:
            for i in range(len(vertical_main) - 1):
                x1 = vertical_main[i].start_point.x
                x2 = vertical_main[i + 1].start_point.x
                
                # 在两条主干道之间添加次干道
                num_secondary = max(1, int((x2 - x1) / self.config.secondary_road_spacing))
                for j in range(num_secondary):
                    x = x1 + (j + 1) * (x2 - x1) / (num_secondary + 1)
                    
                    start = Point(x, 0)
                    end = Point(x, city_map.height)
                    
                    road = Road(
                        f"sec_v_{self.road_counter}",
                        start, end,
                        self.config.secondary_road_width
                    )
                    road.road_type = "avenue"
                    roads.append(road)
                    self.road_counter += 1
        
        # 水平次干道
        if len(horizontal_main) >= 2:
            for i in range(len(horizontal_main) - 1):
                y1 = horizontal_main[i].start_point.y
                y2 = horizontal_main[i + 1].start_point.y
                
                num_secondary = max(1, int((y2 - y1) / self.config.secondary_road_spacing))
                for j in range(num_secondary):
                    y = y1 + (j + 1) * (y2 - y1) / (num_secondary + 1)
                    
                    start = Point(0, y)
                    end = Point(city_map.width, y)
                    
                    road = Road(
                        f"sec_h_{self.road_counter}",
                        start, end,
                        self.config.secondary_road_width
                    )
                    road.road_type = "avenue"
                    roads.append(road)
                    self.road_counter += 1
        
        return roads
    
    def _generate_streets(self, city_map: CityMap, existing_roads: List[Road]) -> List[Road]:
        """生成街道"""
        roads = []
        
        # 获取现有道路的交点
        intersections = self._find_intersections(existing_roads)
        
        # 基于密度参数生成街道
        street_count = int(self.config.street_density * 50)  # 基础街道数量
        
        for _ in range(street_count):
            # 随机选择起点
            if intersections and random.random() < 0.6:
                # 60%概率从交点开始
                start_point = random.choice(intersections)
            else:
                # 40%概率随机起点
                start_point = Point(
                    random.uniform(0, city_map.width),
                    random.uniform(0, city_map.height)
                )
            
            # 生成随机方向和长度
            angle = random.uniform(0, 2 * np.pi)
            length = random.uniform(self.config.min_street_length, self.config.max_street_length)
            
            end_point = Point(
                start_point.x + length * np.cos(angle),
                start_point.y + length * np.sin(angle)
            )
            
            # 确保终点在地图范围内
            end_point.x = max(0, min(city_map.width, end_point.x))
            end_point.y = max(0, min(city_map.height, end_point.y))
            
            # 检查是否与现有道路冲突
            if self._is_valid_street(start_point, end_point, existing_roads + roads):
                road = Road(
                    f"street_{self.road_counter}",
                    start_point, end_point,
                    self.config.street_width
                )
                road.road_type = "street"
                roads.append(road)
                self.road_counter += 1
        
        return roads
    
    def _find_intersections(self, roads: List[Road]) -> List[Point]:
        """找到道路交点"""
        intersections = []
        
        for i, road1 in enumerate(roads):
            for road2 in roads[i+1:]:
                intersection = self._line_intersection(
                    road1.start_point, road1.end_point,
                    road2.start_point, road2.end_point
                )
                if intersection:
                    intersections.append(intersection)
        
        return intersections
    
    def _line_intersection(self, p1: Point, p2: Point, p3: Point, p4: Point) -> Optional[Point]:
        """计算两条线段的交点"""
        x1, y1 = p1.x, p1.y
        x2, y2 = p2.x, p2.y
        x3, y3 = p3.x, p3.y
        x4, y4 = p4.x, p4.y
        
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return None  # 平行线
        
        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
        u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom
        
        if 0 <= t <= 1 and 0 <= u <= 1:
            # 交点在两条线段上
            x = x1 + t * (x2 - x1)
            y = y1 + t * (y2 - y1)
            return Point(x, y)
        
        return None
    
    def _is_valid_street(self, start: Point, end: Point, existing_roads: List[Road]) -> bool:
        """检查街道是否有效（不与现有道路过度重叠）"""
        min_distance = self.config.intersection_buffer
        
        for road in existing_roads:
            # 检查与现有道路的最小距离
            if self._distance_to_line(start, road.start_point, road.end_point) < min_distance:
                return False
            if self._distance_to_line(end, road.start_point, road.end_point) < min_distance:
                return False
        
        return True
    
    def _distance_to_line(self, point: Point, line_start: Point, line_end: Point) -> float:
        """计算点到线段的距离"""
        A = point.x - line_start.x
        B = point.y - line_start.y
        C = line_end.x - line_start.x
        D = line_end.y - line_start.y
        
        dot = A * C + B * D
        len_sq = C * C + D * D
        
        if len_sq == 0:
            return point.distance_to(line_start)
        
        param = dot / len_sq
        
        if param < 0:
            xx = line_start.x
            yy = line_start.y
        elif param > 1:
            xx = line_end.x
            yy = line_end.y
        else:
            xx = line_start.x + param * C
            yy = line_start.y + param * D
        
        dx = point.x - xx
        dy = point.y - yy
        return np.sqrt(dx * dx + dy * dy)
