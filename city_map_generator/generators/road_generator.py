"""
道路生成器

实现道路网络的生成算法
"""

import random
import numpy as np
from typing import List, Tuple, Optional, Set
from ..components import Point, Road, ComponentType
from ..map import CityMap
from ..config import MapConfig, RoadConfig


class RoadGenerator:
    """道路生成器"""
    
    def __init__(self, config: RoadConfig):
        self.config = config
        self.road_counter = 0
    
    def generate_roads(self, city_map: CityMap) -> List[Road]:
        """生成完整的道路网络"""
        roads = []
        
        # 1. 生成主干道
        main_roads = self._generate_main_roads(city_map)
        roads.extend(main_roads)
        
        # 2. 生成次干道
        secondary_roads = self._generate_secondary_roads(city_map, main_roads)
        roads.extend(secondary_roads)
        
        # 3. 生成街道
        streets = self._generate_streets(city_map, main_roads + secondary_roads)
        roads.extend(streets)
        
        # 4. 添加道路到地图
        for road in roads:
            city_map.add_component(road)
        
        return roads
    
    def _generate_main_roads(self, city_map: CityMap) -> List[Road]:
        """生成主干道"""
        roads = []
        
        # 垂直主干道
        vertical_count = max(1, self.config.main_roads_count // 2)
        for i in range(vertical_count):
            x = (i + 1) * city_map.width / (vertical_count + 1)
            start = Point(x, 0)
            end = Point(x, city_map.height)
            
            road = Road(
                f"main_v_{self.road_counter}",
                start, end,
                self.config.main_road_width
            )
            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1
        
        # 水平主干道
        horizontal_count = self.config.main_roads_count - vertical_count
        for i in range(horizontal_count):
            y = (i + 1) * city_map.height / (horizontal_count + 1)
            start = Point(0, y)
            end = Point(city_map.width, y)
            
            road = Road(
                f"main_h_{self.road_counter}",
                start, end,
                self.config.main_road_width
            )
            road.road_type = "highway"
            roads.append(road)
            self.road_counter += 1
        
        return roads
    
    def _generate_secondary_roads(self, city_map: CityMap, main_roads: List[Road]) -> List[Road]:
        """生成次干道"""
        roads = []
        
        # 在主干道之间生成次干道
        vertical_main = [r for r in main_roads if abs(r.start_point.y - 0) < 1]
        horizontal_main = [r for r in main_roads if abs(r.start_point.x - 0) < 1]
        
        # 垂直次干道
        if len(vertical_main) >= 2:
            for i in range(len(vertical_main) - 1):
                x1 = vertical_main[i].start_point.x
                x2 = vertical_main[i + 1].start_point.x
                
                # 在两条主干道之间添加次干道
                num_secondary = max(1, int((x2 - x1) / self.config.secondary_road_spacing))
                for j in range(num_secondary):
                    x = x1 + (j + 1) * (x2 - x1) / (num_secondary + 1)
                    
                    start = Point(x, 0)
                    end = Point(x, city_map.height)
                    
                    road = Road(
                        f"sec_v_{self.road_counter}",
                        start, end,
                        self.config.secondary_road_width
                    )
                    road.road_type = "avenue"
                    roads.append(road)
                    self.road_counter += 1
        
        # 水平次干道
        if len(horizontal_main) >= 2:
            for i in range(len(horizontal_main) - 1):
                y1 = horizontal_main[i].start_point.y
                y2 = horizontal_main[i + 1].start_point.y
                
                num_secondary = max(1, int((y2 - y1) / self.config.secondary_road_spacing))
                for j in range(num_secondary):
                    y = y1 + (j + 1) * (y2 - y1) / (num_secondary + 1)
                    
                    start = Point(0, y)
                    end = Point(city_map.width, y)
                    
                    road = Road(
                        f"sec_h_{self.road_counter}",
                        start, end,
                        self.config.secondary_road_width
                    )
                    road.road_type = "avenue"
                    roads.append(road)
                    self.road_counter += 1
        
        return roads
    
    def _generate_streets(self, city_map: CityMap, existing_roads: List[Road]) -> List[Road]:
        """生成街道"""
        roads = []
        
        # 获取现有道路的交点
        intersections = self._find_intersections(existing_roads)
        
        # 基于密度参数生成街道
        street_count = int(self.config.street_density * 50)  # 基础街道数量
        
        for _ in range(street_count):
            # 随机选择起点
            if intersections and random.random() < 0.6:
                # 60%概率从交点开始
                start_point = random.choice(intersections)
            else:
                # 40%概率随机起点
                start_point = Point(
                    random.uniform(0, city_map.width),
                    random.uniform(0, city_map.height)
                )
            
            # 生成随机方向和长度
            angle = random.uniform(0, 2 * np.pi)
            length = random.uniform(self.config.min_street_length, self.config.max_street_length)
            
            end_point = Point(
                start_point.x + length * np.cos(angle),
                start_point.y + length * np.sin(angle)
            )
            
            # 确保终点在地图范围内
            end_point.x = max(0, min(city_map.width, end_point.x))
            end_point.y = max(0, min(city_map.height, end_point.y))
            
            # 检查是否与现有道路冲突
            if self._is_valid_street(start_point, end_point, existing_roads + roads):
                road = Road(
                    f"street_{self.road_counter}",
                    start_point, end_point,
                    self.config.street_width
                )
                road.road_type = "street"
                roads.append(road)
                self.road_counter += 1
        
        return roads
    
    def _find_intersections(self, roads: List[Road]) -> List[Point]:
        """找到道路交点"""
        intersections = []
        
        for i, road1 in enumerate(roads):
            for road2 in roads[i+1:]:
                intersection = self._line_intersection(
                    road1.start_point, road1.end_point,
                    road2.start_point, road2.end_point
                )
                if intersection:
                    intersections.append(intersection)
        
        return intersections
    
    def _line_intersection(self, p1: Point, p2: Point, p3: Point, p4: Point) -> Optional[Point]:
        """计算两条线段的交点"""
        x1, y1 = p1.x, p1.y
        x2, y2 = p2.x, p2.y
        x3, y3 = p3.x, p3.y
        x4, y4 = p4.x, p4.y
        
        denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4)
        if abs(denom) < 1e-10:
            return None  # 平行线
        
        t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom
        u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom
        
        if 0 <= t <= 1 and 0 <= u <= 1:
            # 交点在两条线段上
            x = x1 + t * (x2 - x1)
            y = y1 + t * (y2 - y1)
            return Point(x, y)
        
        return None
    
    def _is_valid_street(self, start: Point, end: Point, existing_roads: List[Road]) -> bool:
        """检查街道是否有效（不与现有道路过度重叠）"""
        min_distance = self.config.intersection_buffer
        
        for road in existing_roads:
            # 检查与现有道路的最小距离
            if self._distance_to_line(start, road.start_point, road.end_point) < min_distance:
                return False
            if self._distance_to_line(end, road.start_point, road.end_point) < min_distance:
                return False
        
        return True
    
    def _distance_to_line(self, point: Point, line_start: Point, line_end: Point) -> float:
        """计算点到线段的距离"""
        A = point.x - line_start.x
        B = point.y - line_start.y
        C = line_end.x - line_start.x
        D = line_end.y - line_start.y
        
        dot = A * C + B * D
        len_sq = C * C + D * D
        
        if len_sq == 0:
            return point.distance_to(line_start)
        
        param = dot / len_sq
        
        if param < 0:
            xx = line_start.x
            yy = line_start.y
        elif param > 1:
            xx = line_end.x
            yy = line_end.y
        else:
            xx = line_start.x + param * C
            yy = line_start.y + param * D
        
        dx = point.x - xx
        dy = point.y - yy
        return np.sqrt(dx * dx + dy * dy)
