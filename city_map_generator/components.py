"""
城市地图组件定义

包含所有城市元素的基类和具体实现
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum
import numpy as np


class ComponentType(Enum):
    """组件类型枚举"""
    ROAD = "road"
    BUILDING = "building"
    PARK = "park"
    WATER = "water"
    EMPTY = "empty"


@dataclass
class Point:
    """二维点坐标"""
    x: float
    y: float
    
    def distance_to(self, other: 'Point') -> float:
        """计算到另一个点的距离"""
        return np.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)


@dataclass
class Rectangle:
    """矩形区域"""
    x: float
    y: float
    width: float
    height: float
    
    @property
    def center(self) -> Point:
        """获取矩形中心点"""
        return Point(self.x + self.width/2, self.y + self.height/2)
    
    def contains_point(self, point: Point) -> bool:
        """检查点是否在矩形内"""
        return (self.x <= point.x <= self.x + self.width and 
                self.y <= point.y <= self.y + self.height)
    
    def intersects(self, other: 'Rectangle') -> bool:
        """检查是否与另一个矩形相交"""
        return not (self.x + self.width < other.x or 
                   other.x + other.width < self.x or
                   self.y + self.height < other.y or 
                   other.y + other.height < self.y)


class Component(ABC):
    """城市组件基类"""
    
    def __init__(self, component_id: str, position: Point, component_type: ComponentType):
        self.id = component_id
        self.position = position
        self.type = component_type
        self.properties: Dict[str, Any] = {}
    
    @abstractmethod
    def get_bounds(self) -> Rectangle:
        """获取组件的边界矩形"""
        pass
    
    @abstractmethod
    def render_data(self) -> Dict[str, Any]:
        """获取渲染所需的数据"""
        pass
    
    def set_property(self, key: str, value: Any):
        """设置组件属性"""
        self.properties[key] = value
    
    def get_property(self, key: str, default: Any = None) -> Any:
        """获取组件属性"""
        return self.properties.get(key, default)


class Road(Component):
    """道路组件"""
    
    def __init__(self, road_id: str, start_point: Point, end_point: Point, width: float = 2.0):
        super().__init__(road_id, start_point, ComponentType.ROAD)
        self.start_point = start_point
        self.end_point = end_point
        self.width = width
        self.road_type = "street"  # street, avenue, highway
    
    def get_bounds(self) -> Rectangle:
        """获取道路的边界矩形"""
        min_x = min(self.start_point.x, self.end_point.x) - self.width/2
        max_x = max(self.start_point.x, self.end_point.x) + self.width/2
        min_y = min(self.start_point.y, self.end_point.y) - self.width/2
        max_y = max(self.start_point.y, self.end_point.y) + self.width/2
        
        return Rectangle(min_x, min_y, max_x - min_x, max_y - min_y)
    
    def render_data(self) -> Dict[str, Any]:
        """获取道路渲染数据"""
        return {
            'type': 'road',
            'start': (self.start_point.x, self.start_point.y),
            'end': (self.end_point.x, self.end_point.y),
            'width': self.width,
            'road_type': self.road_type,
            'color': self.get_road_color()
        }
    
    def get_road_color(self) -> str:
        """根据道路类型获取颜色"""
        colors = {
            'highway': '#404040',
            'avenue': '#606060', 
            'street': '#808080'
        }
        return colors.get(self.road_type, '#808080')


class Building(Component):
    """建筑物组件"""
    
    def __init__(self, building_id: str, position: Point, width: float, height: float, building_type: str = "residential"):
        super().__init__(building_id, position, ComponentType.BUILDING)
        self.width = width
        self.height = height
        self.building_type = building_type  # residential, commercial, industrial, public
        self.floors = 1
    
    def get_bounds(self) -> Rectangle:
        """获取建筑物的边界矩形"""
        return Rectangle(self.position.x, self.position.y, self.width, self.height)
    
    def render_data(self) -> Dict[str, Any]:
        """获取建筑物渲染数据"""
        return {
            'type': 'building',
            'position': (self.position.x, self.position.y),
            'width': self.width,
            'height': self.height,
            'building_type': self.building_type,
            'floors': self.floors,
            'color': self.get_building_color()
        }
    
    def get_building_color(self) -> str:
        """根据建筑类型获取颜色"""
        colors = {
            'residential': '#8FBC8F',
            'commercial': '#4169E1',
            'industrial': '#A0522D',
            'public': '#DC143C'
        }
        return colors.get(self.building_type, '#8FBC8F')


class Park(Component):
    """公园组件"""
    
    def __init__(self, park_id: str, position: Point, width: float, height: float):
        super().__init__(park_id, position, ComponentType.PARK)
        self.width = width
        self.height = height
        self.park_type = "park"  # park, playground, garden
    
    def get_bounds(self) -> Rectangle:
        """获取公园的边界矩形"""
        return Rectangle(self.position.x, self.position.y, self.width, self.height)
    
    def render_data(self) -> Dict[str, Any]:
        """获取公园渲染数据"""
        return {
            'type': 'park',
            'position': (self.position.x, self.position.y),
            'width': self.width,
            'height': self.height,
            'park_type': self.park_type,
            'color': '#228B22'
        }


class Water(Component):
    """水体组件"""
    
    def __init__(self, water_id: str, position: Point, width: float, height: float):
        super().__init__(water_id, position, ComponentType.WATER)
        self.width = width
        self.height = height
        self.water_type = "lake"  # lake, river, pond
    
    def get_bounds(self) -> Rectangle:
        """获取水体的边界矩形"""
        return Rectangle(self.position.x, self.position.y, self.width, self.height)
    
    def render_data(self) -> Dict[str, Any]:
        """获取水体渲染数据"""
        return {
            'type': 'water',
            'position': (self.position.x, self.position.y),
            'width': self.width,
            'height': self.height,
            'water_type': self.water_type,
            'color': '#4682B4'
        }
