"""
城市地图组件定义

包含所有城市元素的基类和具体实现
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict, Any
from enum import Enum
import numpy as np
import random


class ComponentType(Enum):
    """组件类型枚举"""
    ROAD = "road"
    BUILDING = "building"
    PARK = "park"
    WATER = "water"
    TRANSIT = "transit"  # 公共交通
    EMPTY = "empty"


@dataclass
class Point:
    """二维点坐标"""
    x: float
    y: float
    
    def distance_to(self, other: 'Point') -> float:
        """计算到另一个点的距离"""
        return np.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)


@dataclass
class Rectangle:
    """矩形区域"""
    x: float
    y: float
    width: float
    height: float
    
    @property
    def center(self) -> Point:
        """获取矩形中心点"""
        return Point(self.x + self.width/2, self.y + self.height/2)
    
    def contains_point(self, point: Point) -> bool:
        """检查点是否在矩形内"""
        return (self.x <= point.x <= self.x + self.width and 
                self.y <= point.y <= self.y + self.height)
    
    def intersects(self, other: 'Rectangle') -> bool:
        """检查是否与另一个矩形相交"""
        return not (self.x + self.width < other.x or 
                   other.x + other.width < self.x or
                   self.y + self.height < other.y or 
                   other.y + other.height < self.y)


class Component(ABC):
    """城市组件基类"""
    
    def __init__(self, component_id: str, position: Point, component_type: ComponentType):
        self.id = component_id
        self.position = position
        self.type = component_type
        self.properties: Dict[str, Any] = {}
    
    @abstractmethod
    def get_bounds(self) -> Rectangle:
        """获取组件的边界矩形"""
        pass
    
    @abstractmethod
    def render_data(self) -> Dict[str, Any]:
        """获取渲染所需的数据"""
        pass
    
    def set_property(self, key: str, value: Any):
        """设置组件属性"""
        self.properties[key] = value
    
    def get_property(self, key: str, default: Any = None) -> Any:
        """获取组件属性"""
        return self.properties.get(key, default)


class Road(Component):
    """道路组件 - 支持曲线路径"""

    def __init__(self, road_id: str, waypoints: List[Point], width: float = 2.0):
        # 使用第一个点作为位置
        super().__init__(road_id, waypoints[0] if waypoints else Point(0, 0), ComponentType.ROAD)
        self.waypoints = waypoints  # 路径点列表，支持曲线
        self.width = width
        self.road_type = "street"  # street, avenue, highway

        # 为了兼容性，保留start_point和end_point属性
        self.start_point = waypoints[0] if waypoints else Point(0, 0)
        self.end_point = waypoints[-1] if waypoints else Point(0, 0)
    
    def get_bounds(self) -> Rectangle:
        """获取道路的边界矩形"""
        if not self.waypoints:
            return Rectangle(0, 0, 0, 0)

        min_x = min(p.x for p in self.waypoints) - self.width/2
        max_x = max(p.x for p in self.waypoints) + self.width/2
        min_y = min(p.y for p in self.waypoints) - self.width/2
        max_y = max(p.y for p in self.waypoints) + self.width/2

        return Rectangle(min_x, min_y, max_x - min_x, max_y - min_y)
    
    def render_data(self) -> Dict[str, Any]:
        """获取道路渲染数据"""
        return {
            'type': 'road',
            'waypoints': [(p.x, p.y) for p in self.waypoints],
            'start': (self.start_point.x, self.start_point.y),
            'end': (self.end_point.x, self.end_point.y),
            'width': self.width,
            'road_type': self.road_type,
            'color': self.get_road_color()
        }
    
    def get_road_color(self) -> str:
        """根据道路类型获取颜色"""
        colors = {
            'highway': '#404040',
            'avenue': '#606060', 
            'street': '#808080'
        }
        return colors.get(self.road_type, '#808080')

    def get_total_length(self) -> float:
        """获取道路总长度"""
        if len(self.waypoints) < 2:
            return 0.0

        total_length = 0.0
        for i in range(len(self.waypoints) - 1):
            total_length += self.waypoints[i].distance_to(self.waypoints[i + 1])

        return total_length

    @staticmethod
    def create_straight_road(road_id: str, start: Point, end: Point, width: float = 2.0) -> 'Road':
        """创建直线道路"""
        return Road(road_id, [start, end], width)

    @staticmethod
    def create_curved_road(road_id: str, start: Point, end: Point, curvature: float = 0.2,
                          segments: int = 5, width: float = 2.0) -> 'Road':
        """创建曲线道路"""
        waypoints = []

        # 计算控制点
        mid_x = (start.x + end.x) / 2
        mid_y = (start.y + end.y) / 2

        # 添加随机偏移来创建曲线
        dx = end.x - start.x
        dy = end.y - start.y

        # 垂直方向的偏移
        perp_x = -dy
        perp_y = dx
        length = np.sqrt(perp_x**2 + perp_y**2)

        if length > 0:
            perp_x /= length
            perp_y /= length

            # 随机偏移量
            offset = curvature * np.sqrt(dx**2 + dy**2) * (random.random() - 0.5) * 2
            control_x = mid_x + perp_x * offset
            control_y = mid_y + perp_y * offset
        else:
            control_x = mid_x
            control_y = mid_y

        # 生成贝塞尔曲线点
        for i in range(segments + 1):
            t = i / segments

            # 二次贝塞尔曲线
            x = (1-t)**2 * start.x + 2*(1-t)*t * control_x + t**2 * end.x
            y = (1-t)**2 * start.y + 2*(1-t)*t * control_y + t**2 * end.y

            waypoints.append(Point(x, y))

        return Road(road_id, waypoints, width)

    @staticmethod
    def create_organic_road(road_id: str, start: Point, end: Point,
                           noise_level: float = 0.1, segments: int = 8, width: float = 2.0) -> 'Road':
        """创建有机形状的道路（带噪声）"""
        waypoints = [start]

        dx = end.x - start.x
        dy = end.y - start.y
        total_distance = np.sqrt(dx**2 + dy**2)

        if total_distance == 0:
            return Road(road_id, [start, end], width)

        # 生成中间点
        for i in range(1, segments):
            t = i / segments

            # 基础插值点
            base_x = start.x + t * dx
            base_y = start.y + t * dy

            # 添加噪声
            noise_x = (random.random() - 0.5) * noise_level * total_distance
            noise_y = (random.random() - 0.5) * noise_level * total_distance

            waypoints.append(Point(base_x + noise_x, base_y + noise_y))

        waypoints.append(end)
        return Road(road_id, waypoints, width)


class Building(Component):
    """建筑物组件"""
    
    def __init__(self, building_id: str, position: Point, width: float, height: float, building_type: str = "residential"):
        super().__init__(building_id, position, ComponentType.BUILDING)
        self.width = width
        self.height = height
        self.building_type = building_type  # residential, commercial, industrial, public
        self.floors = 1
    
    def get_bounds(self) -> Rectangle:
        """获取建筑物的边界矩形"""
        return Rectangle(self.position.x, self.position.y, self.width, self.height)
    
    def render_data(self) -> Dict[str, Any]:
        """获取建筑物渲染数据"""
        return {
            'type': 'building',
            'position': (self.position.x, self.position.y),
            'width': self.width,
            'height': self.height,
            'building_type': self.building_type,
            'floors': self.floors,
            'color': self.get_building_color()
        }
    
    def get_building_color(self) -> str:
        """根据建筑类型获取颜色"""
        colors = {
            'residential': '#8FBC8F',
            'commercial': '#4169E1',
            'industrial': '#A0522D',
            'public': '#DC143C'
        }
        return colors.get(self.building_type, '#8FBC8F')


class Park(Component):
    """公园组件"""
    
    def __init__(self, park_id: str, position: Point, width: float, height: float):
        super().__init__(park_id, position, ComponentType.PARK)
        self.width = width
        self.height = height
        self.park_type = "park"  # park, playground, garden
    
    def get_bounds(self) -> Rectangle:
        """获取公园的边界矩形"""
        return Rectangle(self.position.x, self.position.y, self.width, self.height)
    
    def render_data(self) -> Dict[str, Any]:
        """获取公园渲染数据"""
        return {
            'type': 'park',
            'position': (self.position.x, self.position.y),
            'width': self.width,
            'height': self.height,
            'park_type': self.park_type,
            'color': '#228B22'
        }


class Water(Component):
    """水体组件"""
    
    def __init__(self, water_id: str, position: Point, width: float, height: float):
        super().__init__(water_id, position, ComponentType.WATER)
        self.width = width
        self.height = height
        self.water_type = "lake"  # lake, river, pond
    
    def get_bounds(self) -> Rectangle:
        """获取水体的边界矩形"""
        return Rectangle(self.position.x, self.position.y, self.width, self.height)
    
    def render_data(self) -> Dict[str, Any]:
        """获取水体渲染数据"""
        return {
            'type': 'water',
            'position': (self.position.x, self.position.y),
            'width': self.width,
            'height': self.height,
            'water_type': self.water_type,
            'color': '#4682B4'
        }


class TransitLine(Component):
    """公共交通线路组件"""

    def __init__(self, line_id: str, waypoints: List[Point], transit_type: str = "bus"):
        # 使用第一个点作为位置
        super().__init__(line_id, waypoints[0] if waypoints else Point(0, 0), ComponentType.TRANSIT)
        self.waypoints = waypoints
        self.transit_type = transit_type  # bus, subway, tram, light_rail
        self.line_name = ""
        self.stations = []  # 站点列表

    def get_bounds(self) -> Rectangle:
        """获取线路的边界矩形"""
        if not self.waypoints:
            return Rectangle(0, 0, 0, 0)

        min_x = min(p.x for p in self.waypoints)
        max_x = max(p.x for p in self.waypoints)
        min_y = min(p.y for p in self.waypoints)
        max_y = max(p.y for p in self.waypoints)

        return Rectangle(min_x, min_y, max_x - min_x, max_y - min_y)

    def render_data(self) -> Dict[str, Any]:
        """获取交通线路渲染数据"""
        return {
            'type': 'transit',
            'waypoints': [(p.x, p.y) for p in self.waypoints],
            'transit_type': self.transit_type,
            'line_name': self.line_name,
            'stations': [(s.x, s.y) for s in self.stations],
            'color': self.get_transit_color(),
            'width': self.get_line_width()
        }

    def get_transit_color(self) -> str:
        """根据交通类型获取颜色"""
        colors = {
            'subway': '#FF6B35',    # 橙红色
            'bus': '#4ECDC4',       # 青绿色
            'tram': '#45B7D1',      # 蓝色
            'light_rail': '#96CEB4' # 浅绿色
        }
        return colors.get(self.transit_type, '#4ECDC4')

    def get_line_width(self) -> float:
        """根据交通类型获取线宽"""
        widths = {
            'subway': 4.0,
            'bus': 2.0,
            'tram': 3.0,
            'light_rail': 3.5
        }
        return widths.get(self.transit_type, 2.0)

    def add_station(self, position: Point, station_name: str = ""):
        """添加站点"""
        self.stations.append(position)
        self.set_property(f"station_{len(self.stations)}", {
            'name': station_name or f"Station {len(self.stations)}",
            'position': (position.x, position.y)
        })

    def get_total_length(self) -> float:
        """获取线路总长度"""
        if len(self.waypoints) < 2:
            return 0.0

        total_length = 0.0
        for i in range(len(self.waypoints) - 1):
            total_length += self.waypoints[i].distance_to(self.waypoints[i + 1])

        return total_length


class TransitStation(Component):
    """公共交通站点组件"""

    def __init__(self, station_id: str, position: Point, station_type: str = "bus_stop"):
        super().__init__(station_id, position, ComponentType.TRANSIT)
        self.station_type = station_type  # bus_stop, subway_station, tram_stop
        self.station_name = ""
        self.connected_lines = []  # 连接的线路ID列表
        self.size = self.get_station_size()

    def get_bounds(self) -> Rectangle:
        """获取站点的边界矩形"""
        return Rectangle(
            self.position.x - self.size/2,
            self.position.y - self.size/2,
            self.size, self.size
        )

    def render_data(self) -> Dict[str, Any]:
        """获取站点渲染数据"""
        return {
            'type': 'transit_station',
            'position': (self.position.x, self.position.y),
            'station_type': self.station_type,
            'station_name': self.station_name,
            'size': self.size,
            'color': self.get_station_color(),
            'connected_lines': len(self.connected_lines)
        }

    def get_station_size(self) -> float:
        """根据站点类型获取大小"""
        sizes = {
            'subway_station': 8.0,
            'bus_stop': 3.0,
            'tram_stop': 4.0,
            'light_rail_station': 6.0
        }
        return sizes.get(self.station_type, 3.0)

    def get_station_color(self) -> str:
        """根据站点类型获取颜色"""
        colors = {
            'subway_station': '#D32F2F',
            'bus_stop': '#1976D2',
            'tram_stop': '#388E3C',
            'light_rail_station': '#7B1FA2'
        }
        return colors.get(self.station_type, '#1976D2')

    def connect_line(self, line_id: str):
        """连接交通线路"""
        if line_id not in self.connected_lines:
            self.connected_lines.append(line_id)
