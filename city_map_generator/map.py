"""
城市地图核心类

管理所有城市组件和地图生成逻辑
"""

from typing import List, Dict, Optional, Tuple, Set
import numpy as np
from .components import Component, ComponentType, Point, Rectangle, Road, Building, Park, Water


class CityMap:
    """城市地图主类"""
    
    def __init__(self, width: float, height: float, name: str = "Generated City"):
        self.width = width
        self.height = height
        self.name = name
        
        # 存储所有组件
        self.components: Dict[str, Component] = {}
        self.components_by_type: Dict[ComponentType, List[Component]] = {
            ComponentType.ROAD: [],
            ComponentType.BUILDING: [],
            ComponentType.PARK: [],
            ComponentType.WATER: [],
            ComponentType.EMPTY: []
        }
        
        # 空间索引 - 简单的网格系统
        self.grid_size = 50.0  # 网格大小
        self.spatial_grid: Dict[Tuple[int, int], List[Component]] = {}
        
        # 地图统计信息
        self.stats = {
            'total_components': 0,
            'roads_length': 0.0,
            'buildings_count': 0,
            'parks_count': 0,
            'water_count': 0
        }
    
    def add_component(self, component: Component) -> bool:
        """添加组件到地图"""
        if component.id in self.components:
            return False
        
        # 检查边界
        bounds = component.get_bounds()
        if not self._is_within_bounds(bounds):
            return False
        
        # 添加到主存储
        self.components[component.id] = component
        self.components_by_type[component.type].append(component)
        
        # 添加到空间索引
        self._add_to_spatial_grid(component)
        
        # 更新统计信息
        self._update_stats(component, add=True)
        
        return True
    
    def remove_component(self, component_id: str) -> bool:
        """从地图中移除组件"""
        if component_id not in self.components:
            return False
        
        component = self.components[component_id]
        
        # 从主存储移除
        del self.components[component_id]
        self.components_by_type[component.type].remove(component)
        
        # 从空间索引移除
        self._remove_from_spatial_grid(component)
        
        # 更新统计信息
        self._update_stats(component, add=False)
        
        return True
    
    def get_components_by_type(self, component_type: ComponentType) -> List[Component]:
        """获取指定类型的所有组件"""
        return self.components_by_type[component_type].copy()
    
    def get_components_in_area(self, area: Rectangle) -> List[Component]:
        """获取指定区域内的所有组件"""
        components = []
        
        # 计算涉及的网格范围
        start_x = int(area.x // self.grid_size)
        end_x = int((area.x + area.width) // self.grid_size) + 1
        start_y = int(area.y // self.grid_size)
        end_y = int((area.y + area.height) // self.grid_size) + 1
        
        # 检查相关网格
        for gx in range(start_x, end_x):
            for gy in range(start_y, end_y):
                if (gx, gy) in self.spatial_grid:
                    for component in self.spatial_grid[(gx, gy)]:
                        if area.intersects(component.get_bounds()):
                            components.append(component)
        
        return list(set(components))  # 去重
    
    def find_nearest_component(self, point: Point, component_type: Optional[ComponentType] = None) -> Optional[Component]:
        """找到最近的组件"""
        min_distance = float('inf')
        nearest_component = None
        
        components = (self.components_by_type[component_type] if component_type 
                     else [comp for comps in self.components_by_type.values() for comp in comps])
        
        for component in components:
            bounds = component.get_bounds()
            center = bounds.center
            distance = point.distance_to(center)
            
            if distance < min_distance:
                min_distance = distance
                nearest_component = component
        
        return nearest_component
    
    def check_collision(self, bounds: Rectangle, exclude_types: Optional[Set[ComponentType]] = None) -> bool:
        """检查区域是否与现有组件冲突"""
        exclude_types = exclude_types or set()
        
        components_in_area = self.get_components_in_area(bounds)
        
        for component in components_in_area:
            if component.type not in exclude_types:
                if bounds.intersects(component.get_bounds()):
                    return True
        
        return False
    
    def get_available_space(self, min_width: float, min_height: float) -> List[Rectangle]:
        """获取可用的空间区域"""
        available_spaces = []
        
        # 简单的网格扫描算法
        step_size = min(min_width, min_height) / 2
        
        for x in np.arange(0, self.width - min_width, step_size):
            for y in np.arange(0, self.height - min_height, step_size):
                test_rect = Rectangle(x, y, min_width, min_height)
                
                if not self.check_collision(test_rect):
                    available_spaces.append(test_rect)
        
        return available_spaces
    
    def get_map_bounds(self) -> Rectangle:
        """获取地图边界"""
        return Rectangle(0, 0, self.width, self.height)
    
    def get_stats(self) -> Dict:
        """获取地图统计信息"""
        return self.stats.copy()
    
    def clear(self):
        """清空地图"""
        self.components.clear()
        for component_list in self.components_by_type.values():
            component_list.clear()
        self.spatial_grid.clear()
        
        self.stats = {
            'total_components': 0,
            'roads_length': 0.0,
            'buildings_count': 0,
            'parks_count': 0,
            'water_count': 0
        }
    
    def _is_within_bounds(self, bounds: Rectangle) -> bool:
        """检查边界是否在地图范围内"""
        return (bounds.x >= 0 and bounds.y >= 0 and 
                bounds.x + bounds.width <= self.width and 
                bounds.y + bounds.height <= self.height)
    
    def _get_grid_coords(self, point: Point) -> Tuple[int, int]:
        """获取点的网格坐标"""
        return (int(point.x // self.grid_size), int(point.y // self.grid_size))
    
    def _add_to_spatial_grid(self, component: Component):
        """将组件添加到空间索引"""
        bounds = component.get_bounds()
        
        # 计算组件覆盖的网格范围
        start_x = int(bounds.x // self.grid_size)
        end_x = int((bounds.x + bounds.width) // self.grid_size) + 1
        start_y = int(bounds.y // self.grid_size)
        end_y = int((bounds.y + bounds.height) // self.grid_size) + 1
        
        for gx in range(start_x, end_x):
            for gy in range(start_y, end_y):
                if (gx, gy) not in self.spatial_grid:
                    self.spatial_grid[(gx, gy)] = []
                self.spatial_grid[(gx, gy)].append(component)
    
    def _remove_from_spatial_grid(self, component: Component):
        """从空间索引中移除组件"""
        bounds = component.get_bounds()
        
        start_x = int(bounds.x // self.grid_size)
        end_x = int((bounds.x + bounds.width) // self.grid_size) + 1
        start_y = int(bounds.y // self.grid_size)
        end_y = int((bounds.y + bounds.height) // self.grid_size) + 1
        
        for gx in range(start_x, end_x):
            for gy in range(start_y, end_y):
                if (gx, gy) in self.spatial_grid and component in self.spatial_grid[(gx, gy)]:
                    self.spatial_grid[(gx, gy)].remove(component)
                    if not self.spatial_grid[(gx, gy)]:
                        del self.spatial_grid[(gx, gy)]
    
    def _update_stats(self, component: Component, add: bool = True):
        """更新统计信息"""
        multiplier = 1 if add else -1
        
        self.stats['total_components'] += multiplier
        
        if component.type == ComponentType.ROAD:
            road = component
            if hasattr(road, 'start_point') and hasattr(road, 'end_point'):
                length = road.start_point.distance_to(road.end_point)
                self.stats['roads_length'] += length * multiplier
        elif component.type == ComponentType.BUILDING:
            self.stats['buildings_count'] += multiplier
        elif component.type == ComponentType.PARK:
            self.stats['parks_count'] += multiplier
        elif component.type == ComponentType.WATER:
            self.stats['water_count'] += multiplier
