"""
地图生成配置系统

定义所有可配置的参数和默认值
"""

from dataclasses import dataclass, field
from typing import Dict, List, Tuple, Optional
import json


@dataclass
class RoadConfig:
    """道路生成配置"""
    # 主干道配置
    main_roads_count: int = 3
    main_road_width: float = 6.0
    main_road_spacing: float = 100.0
    
    # 次干道配置
    secondary_roads_count: int = 8
    secondary_road_width: float = 4.0
    secondary_road_spacing: float = 50.0
    
    # 小路配置
    street_density: float = 0.3  # 0-1之间
    street_width: float = 2.0
    min_street_length: float = 20.0
    max_street_length: float = 80.0
    
    # 道路连接
    connection_probability: float = 0.7
    intersection_buffer: float = 5.0


@dataclass
class BuildingConfig:
    """建筑物生成配置"""
    # 建筑密度
    building_density: float = 0.4  # 0-1之间
    
    # 建筑类型比例
    residential_ratio: float = 0.6
    commercial_ratio: float = 0.25
    industrial_ratio: float = 0.1
    public_ratio: float = 0.05
    
    # 建筑尺寸范围
    min_building_size: Tuple[float, float] = (8.0, 8.0)
    max_building_size: Tuple[float, float] = (25.0, 25.0)
    
    # 特殊建筑
    skyscraper_probability: float = 0.05
    skyscraper_min_size: Tuple[float, float] = (15.0, 15.0)
    
    # 建筑间距
    min_building_spacing: float = 3.0
    road_setback: float = 5.0  # 距离道路的最小距离


@dataclass
class ParkConfig:
    """公园生成配置"""
    # 公园数量和密度
    park_density: float = 0.15  # 占地图面积的比例
    min_parks_count: int = 2
    max_parks_count: int = 8
    
    # 公园尺寸
    min_park_size: Tuple[float, float] = (20.0, 20.0)
    max_park_size: Tuple[float, float] = (80.0, 80.0)
    
    # 公园类型
    large_park_probability: float = 0.3
    playground_probability: float = 0.4
    garden_probability: float = 0.3


@dataclass
class WaterConfig:
    """水体生成配置"""
    # 水体生成概率
    water_probability: float = 0.3
    
    # 水体数量
    min_water_bodies: int = 0
    max_water_bodies: int = 3
    
    # 水体尺寸
    min_water_size: Tuple[float, float] = (15.0, 15.0)
    max_water_size: Tuple[float, float] = (60.0, 40.0)
    
    # 水体类型
    lake_probability: float = 0.6
    river_probability: float = 0.3
    pond_probability: float = 0.1


@dataclass
class MapConfig:
    """地图生成主配置"""
    # 地图基本参数
    width: float = 500.0
    height: float = 500.0
    name: str = "Generated City"
    
    # 随机种子
    random_seed: Optional[int] = None
    
    # 子配置
    roads: RoadConfig = field(default_factory=RoadConfig)
    buildings: BuildingConfig = field(default_factory=BuildingConfig)
    parks: ParkConfig = field(default_factory=ParkConfig)
    water: WaterConfig = field(default_factory=WaterConfig)
    
    # 生成算法参数
    generation_iterations: int = 100
    optimization_passes: int = 3
    
    # 质量控制
    min_component_distance: float = 1.0
    overlap_tolerance: float = 0.1
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            'width': self.width,
            'height': self.height,
            'name': self.name,
            'random_seed': self.random_seed,
            'roads': {
                'main_roads_count': self.roads.main_roads_count,
                'main_road_width': self.roads.main_road_width,
                'main_road_spacing': self.roads.main_road_spacing,
                'secondary_roads_count': self.roads.secondary_roads_count,
                'secondary_road_width': self.roads.secondary_road_width,
                'secondary_road_spacing': self.roads.secondary_road_spacing,
                'street_density': self.roads.street_density,
                'street_width': self.roads.street_width,
                'min_street_length': self.roads.min_street_length,
                'max_street_length': self.roads.max_street_length,
                'connection_probability': self.roads.connection_probability,
                'intersection_buffer': self.roads.intersection_buffer
            },
            'buildings': {
                'building_density': self.buildings.building_density,
                'residential_ratio': self.buildings.residential_ratio,
                'commercial_ratio': self.buildings.commercial_ratio,
                'industrial_ratio': self.buildings.industrial_ratio,
                'public_ratio': self.buildings.public_ratio,
                'min_building_size': self.buildings.min_building_size,
                'max_building_size': self.buildings.max_building_size,
                'skyscraper_probability': self.buildings.skyscraper_probability,
                'skyscraper_min_size': self.buildings.skyscraper_min_size,
                'min_building_spacing': self.buildings.min_building_spacing,
                'road_setback': self.buildings.road_setback
            },
            'parks': {
                'park_density': self.parks.park_density,
                'min_parks_count': self.parks.min_parks_count,
                'max_parks_count': self.parks.max_parks_count,
                'min_park_size': self.parks.min_park_size,
                'max_park_size': self.parks.max_park_size,
                'large_park_probability': self.parks.large_park_probability,
                'playground_probability': self.parks.playground_probability,
                'garden_probability': self.parks.garden_probability
            },
            'water': {
                'water_probability': self.water.water_probability,
                'min_water_bodies': self.water.min_water_bodies,
                'max_water_bodies': self.water.max_water_bodies,
                'min_water_size': self.water.min_water_size,
                'max_water_size': self.water.max_water_size,
                'lake_probability': self.water.lake_probability,
                'river_probability': self.water.river_probability,
                'pond_probability': self.water.pond_probability
            },
            'generation_iterations': self.generation_iterations,
            'optimization_passes': self.optimization_passes,
            'min_component_distance': self.min_component_distance,
            'overlap_tolerance': self.overlap_tolerance
        }
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'MapConfig':
        """从字典创建配置"""
        config = cls()
        
        # 基本参数
        config.width = data.get('width', config.width)
        config.height = data.get('height', config.height)
        config.name = data.get('name', config.name)
        config.random_seed = data.get('random_seed', config.random_seed)
        
        # 道路配置
        if 'roads' in data:
            roads_data = data['roads']
            config.roads.main_roads_count = roads_data.get('main_roads_count', config.roads.main_roads_count)
            config.roads.main_road_width = roads_data.get('main_road_width', config.roads.main_road_width)
            # ... 其他道路参数
        
        # 建筑配置
        if 'buildings' in data:
            buildings_data = data['buildings']
            config.buildings.building_density = buildings_data.get('building_density', config.buildings.building_density)
            # ... 其他建筑参数
        
        # 公园配置
        if 'parks' in data:
            parks_data = data['parks']
            config.parks.park_density = parks_data.get('park_density', config.parks.park_density)
            # ... 其他公园参数
        
        # 水体配置
        if 'water' in data:
            water_data = data['water']
            config.water.water_probability = water_data.get('water_probability', config.water.water_probability)
            # ... 其他水体参数
        
        return config
    
    def save_to_file(self, filename: str):
        """保存配置到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.to_dict(), f, indent=2, ensure_ascii=False)
    
    @classmethod
    def load_from_file(cls, filename: str) -> 'MapConfig':
        """从文件加载配置"""
        with open(filename, 'r', encoding='utf-8') as f:
            data = json.load(f)
        return cls.from_dict(data)


# 预定义的配置模板
SMALL_CITY_CONFIG = MapConfig(
    width=300.0,
    height=300.0,
    name="Small City"
)

MEDIUM_CITY_CONFIG = MapConfig(
    width=500.0,
    height=500.0,
    name="Medium City"
)

LARGE_CITY_CONFIG = MapConfig(
    width=800.0,
    height=800.0,
    name="Large City"
)

DENSE_CITY_CONFIG = MapConfig(
    width=500.0,
    height=500.0,
    name="Dense City"
)
DENSE_CITY_CONFIG.buildings.building_density = 0.7
DENSE_CITY_CONFIG.roads.street_density = 0.5

GREEN_CITY_CONFIG = MapConfig(
    width=500.0,
    height=500.0,
    name="Green City"
)
GREEN_CITY_CONFIG.parks.park_density = 0.25
GREEN_CITY_CONFIG.buildings.building_density = 0.3
