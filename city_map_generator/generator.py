"""
城市地图生成器主类

整合所有生成器，提供统一的生成接口
"""

import random
import time
from typing import Optional, Dict, Any
import os
from .map import CityMap
from .config import MapConfig
from .generators import RoadGenerator, BuildingGenerator, ParkGenerator, WaterGenerator, TransitGenerator
from .visualizer import MapVisualizer
from .zoning import ZoningSystem


class CityMapGenerator:
    """城市地图生成器主类"""
    
    def __init__(self, config: Optional[MapConfig] = None):
        self.config = config or MapConfig()
        
        # 初始化分区系统
        self.zoning_system = ZoningSystem()

        # 初始化生成器
        self.road_generator = RoadGenerator(self.config.roads)
        self.building_generator = BuildingGenerator(self.config.buildings, self.zoning_system)
        self.park_generator = ParkGenerator(self.config.parks)
        self.water_generator = WaterGenerator(self.config.water)
        self.transit_generator = TransitGenerator()
        
        # 初始化可视化器
        self.visualizer = MapVisualizer()
        
        # 设置随机种子
        if self.config.random_seed is not None:
            random.seed(self.config.random_seed)
    
    def generate_city(self, progress_callback: Optional[callable] = None) -> CityMap:
        """生成完整的城市地图"""
        print(f"开始生成城市: {self.config.name}")
        print(f"地图尺寸: {self.config.width} x {self.config.height}")
        
        start_time = time.time()
        
        # 创建地图
        city_map = CityMap(self.config.width, self.config.height, self.config.name)
        
        # 生成步骤 - 更真实的城市发展顺序
        steps = [
            ("规划城市分区", self._create_zoning),
            ("生成基础地形和水体", self._generate_water),
            ("建设道路网络", self._generate_roads),
            ("沿路发展建筑", self._generate_buildings),
            ("建设公共交通", self._generate_transit),
            ("添加公园绿地", self._generate_parks),
            ("优化城市布局", self._optimize_layout)
        ]
        
        total_steps = len(steps)
        
        for i, (step_name, step_func) in enumerate(steps):
            print(f"步骤 {i+1}/{total_steps}: {step_name}...")
            
            if progress_callback:
                progress_callback(i / total_steps, step_name)
            
            step_func(city_map)
            
            print(f"  完成 - 当前组件数: {city_map.stats['total_components']}")
        
        # 最终统计
        generation_time = time.time() - start_time
        stats = city_map.get_stats()
        
        print(f"\n城市生成完成! 耗时: {generation_time:.2f}秒")
        print(f"最终统计:")
        print(f"  - 建筑物: {stats['buildings_count']}")
        print(f"  - 公园: {stats['parks_count']}")
        print(f"  - 水体: {stats['water_count']}")
        print(f"  - 道路总长: {stats['roads_length']:.1f}米")
        print(f"  - 总组件数: {stats['total_components']}")
        
        if progress_callback:
            progress_callback(1.0, "生成完成")
        
        return city_map

    def _create_zoning(self, city_map: CityMap):
        """创建城市分区"""
        zones = self.zoning_system.create_city_zones(city_map.width, city_map.height)
        print(f"  创建了 {len(zones)} 个功能分区")
    
    def _generate_water(self, city_map: CityMap):
        """生成水体"""
        water_bodies = self.water_generator.generate_water_bodies(city_map)
        
        # 可选：生成河流系统
        if random.random() < 0.3:  # 30%概率生成河流系统
            river_system = self.water_generator.generate_river_system(city_map)
            water_bodies.extend(river_system)
    
    def _generate_roads(self, city_map: CityMap):
        """生成道路网络"""
        roads = self.road_generator.generate_roads(city_map)
    
    def _generate_parks(self, city_map: CityMap):
        """生成公园"""
        parks = self.park_generator.generate_parks(city_map)
        
        # 可选：生成公园系统
        if random.random() < 0.4:  # 40%概率生成连接的公园系统
            park_system = self.park_generator.generate_park_system(city_map)
            parks.extend(park_system)
    
    def _generate_buildings(self, city_map: CityMap):
        """生成建筑物 - 沿道路发展"""
        buildings = self.building_generator.generate_buildings(city_map)

        # 可选：生成建筑群（在有足够空间的情况下）
        if len(buildings) < 100:  # 只有在建筑不太密集时才生成建筑群
            cluster_count = random.randint(1, 2)
            for _ in range(cluster_count):
                # 随机选择建筑群中心
                center_x = random.uniform(city_map.width * 0.2, city_map.width * 0.8)
                center_y = random.uniform(city_map.height * 0.2, city_map.height * 0.8)
                from .components import Point
                center = Point(center_x, center_y)

                cluster_type = random.choice(["residential", "commercial"])
                cluster_buildings = self.building_generator.generate_building_cluster(
                    city_map, center, cluster_type, random.randint(3, 5)
                )
                buildings.extend(cluster_buildings)

    def _generate_transit(self, city_map: CityMap):
        """生成公共交通系统"""
        # 只有在城市足够大时才生成公共交通
        if city_map.width >= 400 or city_map.height >= 400:
            lines, stations = self.transit_generator.generate_transit_system(city_map)
            print(f"  生成了 {len(lines)} 条交通线路和 {len(stations)} 个站点")
    
    def _optimize_layout(self, city_map: CityMap):
        """优化布局"""
        # 简单的优化：移除过于拥挤的组件
        optimization_passes = self.config.optimization_passes
        
        for pass_num in range(optimization_passes):
            components_to_remove = []
            
            for component_id, component in city_map.components.items():
                bounds = component.get_bounds()
                
                # 检查是否过于拥挤
                nearby_components = city_map.get_components_in_area(bounds)
                
                if len(nearby_components) > 10:  # 如果周围组件太多
                    # 保留重要组件（道路、大型建筑、公园）
                    if (component.type.value not in ['road', 'park'] and 
                        bounds.width * bounds.height < 200):
                        components_to_remove.append(component_id)
            
            # 移除拥挤的小组件
            for component_id in components_to_remove[:5]:  # 每次最多移除5个
                city_map.remove_component(component_id)
    
    def visualize_city(self, city_map: CityMap, show_zones: bool = False, **kwargs) -> None:
        """可视化城市地图"""
        self.visualizer.visualize_map(city_map, zoning_system=self.zoning_system,
                                     show_zones=show_zones, **kwargs)
        self.visualizer.show_map(city_map)
    
    def save_city_image(self, city_map: CityMap, filename: str, show_zones: bool = False, **kwargs) -> None:
        """保存城市地图图片"""
        self.visualizer.save_map(filename, city_map, zoning_system=self.zoning_system,
                                show_zones=show_zones, **kwargs)
    
    def generate_and_save(self, output_dir: str = "output",
                         show_visualization: bool = True,
                         save_config: bool = True,
                         show_zones: bool = False) -> CityMap:
        """生成城市并保存结果"""
        import os
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成城市
        city_map = self.generate_city()
        
        # 保存配置
        if save_config:
            config_file = os.path.join(output_dir, f"{city_map.name}_config.json")
            self.config.save_to_file(config_file)
            print(f"配置已保存到: {config_file}")
        
        # 保存地图图片
        image_file = os.path.join(output_dir, f"{city_map.name}_map.png")
        self.save_city_image(city_map, image_file, show_zones=show_zones)

        # 显示可视化
        if show_visualization:
            self.visualize_city(city_map, show_zones=show_zones)
        
        return city_map
    
    def generate_multiple_cities(self, count: int, output_dir: str = "output",
                                base_config: Optional[MapConfig] = None) -> list:
        """生成多个城市"""
        cities = []
        base_config = base_config or self.config
        
        for i in range(count):
            # 为每个城市创建略有不同的配置
            config = MapConfig()
            config.name = f"City_{i+1:03d}"
            config.random_seed = random.randint(1, 1000000)
            
            # 随机调整一些参数
            config.buildings.building_density *= random.uniform(0.8, 1.2)
            config.parks.park_density *= random.uniform(0.7, 1.3)
            config.roads.street_density *= random.uniform(0.8, 1.2)
            
            # 创建生成器并生成城市
            generator = CityMapGenerator(config)
            city_map = generator.generate_and_save(
                output_dir=os.path.join(output_dir, config.name),
                show_visualization=False
            )
            
            cities.append(city_map)
            print(f"已生成城市 {i+1}/{count}: {config.name}")
        
        return cities
