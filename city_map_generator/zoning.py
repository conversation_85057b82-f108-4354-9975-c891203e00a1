"""
城市分区系统

实现商业区、住宅区、工业区等功能分区
"""

import random
import numpy as np
from typing import List, Dict, Tuple, Optional
from enum import Enum
from dataclasses import dataclass

from .components import Point, Rectangle


class ZoneType(Enum):
    """分区类型枚举"""
    RESIDENTIAL = "residential"    # 住宅区
    COMMERCIAL = "commercial"      # 商业区
    INDUSTRIAL = "industrial"      # 工业区
    MIXED_USE = "mixed_use"       # 混合用途区
    CIVIC = "civic"               # 公共设施区
    RECREATION = "recreation"      # 娱乐休闲区


@dataclass
class Zone:
    """城市分区"""
    zone_id: str
    zone_type: ZoneType
    bounds: Rectangle
    density: float = 0.5  # 建筑密度 0.0-1.0
    height_limit: int = 10  # 建筑高度限制（层数）
    
    def __post_init__(self):
        self.properties = {}
        self._set_zone_properties()
    
    def _set_zone_properties(self):
        """根据分区类型设置属性"""
        if self.zone_type == ZoneType.RESIDENTIAL:
            self.properties.update({
                'building_types': ['residential'],
                'preferred_density': 0.4,
                'height_limit': 8,
                'noise_tolerance': 'low',
                'green_space_requirement': 0.2
            })
        elif self.zone_type == ZoneType.COMMERCIAL:
            self.properties.update({
                'building_types': ['commercial'],
                'preferred_density': 0.7,
                'height_limit': 20,
                'noise_tolerance': 'medium',
                'parking_requirement': 'high'
            })
        elif self.zone_type == ZoneType.INDUSTRIAL:
            self.properties.update({
                'building_types': ['industrial'],
                'preferred_density': 0.5,
                'height_limit': 6,
                'noise_tolerance': 'high',
                'buffer_zone_required': True
            })
        elif self.zone_type == ZoneType.MIXED_USE:
            self.properties.update({
                'building_types': ['residential', 'commercial'],
                'preferred_density': 0.6,
                'height_limit': 15,
                'noise_tolerance': 'medium'
            })
        elif self.zone_type == ZoneType.CIVIC:
            self.properties.update({
                'building_types': ['public'],
                'preferred_density': 0.3,
                'height_limit': 12,
                'accessibility_required': True
            })
        elif self.zone_type == ZoneType.RECREATION:
            self.properties.update({
                'building_types': ['public'],
                'preferred_density': 0.1,
                'height_limit': 4,
                'green_space_requirement': 0.6
            })
    
    def get_allowed_building_types(self) -> List[str]:
        """获取允许的建筑类型"""
        return self.properties.get('building_types', ['residential'])
    
    def get_building_density(self) -> float:
        """获取建筑密度"""
        return self.properties.get('preferred_density', self.density)
    
    def get_height_limit(self) -> int:
        """获取高度限制"""
        return self.properties.get('height_limit', self.height_limit)


class ZoningSystem:
    """城市分区系统"""
    
    def __init__(self):
        self.zones: List[Zone] = []
        self.zone_counter = 0
    
    def create_city_zones(self, city_width: float, city_height: float) -> List[Zone]:
        """为城市创建分区"""
        self.zones.clear()
        
        # 创建基本的分区布局
        zones = []
        
        # 1. 市中心商业区
        cbd_zone = self._create_cbd_zone(city_width, city_height)
        zones.append(cbd_zone)
        
        # 2. 住宅区
        residential_zones = self._create_residential_zones(city_width, city_height)
        zones.extend(residential_zones)
        
        # 3. 工业区
        industrial_zones = self._create_industrial_zones(city_width, city_height)
        zones.extend(industrial_zones)
        
        # 4. 混合用途区
        mixed_zones = self._create_mixed_use_zones(city_width, city_height)
        zones.extend(mixed_zones)
        
        # 5. 公共设施区
        civic_zones = self._create_civic_zones(city_width, city_height)
        zones.extend(civic_zones)
        
        self.zones = zones
        return zones
    
    def _create_cbd_zone(self, city_width: float, city_height: float) -> Zone:
        """创建中央商务区"""
        center_x = city_width / 2
        center_y = city_height / 2
        
        # CBD通常占城市中心的15-20%
        cbd_size = min(city_width, city_height) * 0.2
        
        bounds = Rectangle(
            center_x - cbd_size/2,
            center_y - cbd_size/2,
            cbd_size,
            cbd_size
        )
        
        zone = Zone(
            f"cbd_{self.zone_counter}",
            ZoneType.COMMERCIAL,
            bounds,
            density=0.8
        )
        zone.height_limit = 30  # 允许高层建筑
        self.zone_counter += 1
        
        return zone
    
    def _create_residential_zones(self, city_width: float, city_height: float) -> List[Zone]:
        """创建住宅区"""
        zones = []
        
        # 在城市四个象限创建住宅区
        quadrants = [
            (0.1, 0.1, 0.35, 0.35),  # 左下
            (0.55, 0.1, 0.35, 0.35), # 右下
            (0.1, 0.55, 0.35, 0.35), # 左上
            (0.55, 0.55, 0.35, 0.35) # 右上
        ]
        
        for i, (x_ratio, y_ratio, w_ratio, h_ratio) in enumerate(quadrants):
            bounds = Rectangle(
                city_width * x_ratio,
                city_height * y_ratio,
                city_width * w_ratio,
                city_height * h_ratio
            )
            
            # 不同密度的住宅区
            density = random.choice([0.3, 0.4, 0.5])  # 低、中、高密度
            
            zone = Zone(
                f"residential_{self.zone_counter}",
                ZoneType.RESIDENTIAL,
                bounds,
                density=density
            )
            zones.append(zone)
            self.zone_counter += 1
        
        return zones
    
    def _create_industrial_zones(self, city_width: float, city_height: float) -> List[Zone]:
        """创建工业区"""
        zones = []
        
        # 工业区通常在城市边缘
        industrial_areas = [
            (0.05, 0.05, 0.25, 0.15),  # 左下角
            (0.7, 0.8, 0.25, 0.15),    # 右上角
        ]
        
        for i, (x_ratio, y_ratio, w_ratio, h_ratio) in enumerate(industrial_areas):
            bounds = Rectangle(
                city_width * x_ratio,
                city_height * y_ratio,
                city_width * w_ratio,
                city_height * h_ratio
            )
            
            zone = Zone(
                f"industrial_{self.zone_counter}",
                ZoneType.INDUSTRIAL,
                bounds,
                density=0.4
            )
            zones.append(zone)
            self.zone_counter += 1
        
        return zones
    
    def _create_mixed_use_zones(self, city_width: float, city_height: float) -> List[Zone]:
        """创建混合用途区"""
        zones = []
        
        # 在商业区和住宅区之间创建混合用途区
        mixed_areas = [
            (0.25, 0.25, 0.2, 0.2),   # 中心偏左下
            (0.55, 0.55, 0.2, 0.2),   # 中心偏右上
        ]
        
        for i, (x_ratio, y_ratio, w_ratio, h_ratio) in enumerate(mixed_areas):
            bounds = Rectangle(
                city_width * x_ratio,
                city_height * y_ratio,
                city_width * w_ratio,
                city_height * h_ratio
            )
            
            zone = Zone(
                f"mixed_use_{self.zone_counter}",
                ZoneType.MIXED_USE,
                bounds,
                density=0.6
            )
            zones.append(zone)
            self.zone_counter += 1
        
        return zones
    
    def _create_civic_zones(self, city_width: float, city_height: float) -> List[Zone]:
        """创建公共设施区"""
        zones = []
        
        # 在城市中心附近创建公共设施区
        civic_areas = [
            (0.4, 0.15, 0.2, 0.15),   # 市政中心
            (0.15, 0.4, 0.15, 0.2),   # 教育区
        ]
        
        for i, (x_ratio, y_ratio, w_ratio, h_ratio) in enumerate(civic_areas):
            bounds = Rectangle(
                city_width * x_ratio,
                city_height * y_ratio,
                city_width * w_ratio,
                city_height * h_ratio
            )
            
            zone = Zone(
                f"civic_{self.zone_counter}",
                ZoneType.CIVIC,
                bounds,
                density=0.3
            )
            zones.append(zone)
            self.zone_counter += 1
        
        return zones
    
    def get_zone_at_position(self, position: Point) -> Optional[Zone]:
        """获取指定位置的分区"""
        for zone in self.zones:
            if zone.bounds.contains_point(position):
                return zone
        return None
    
    def get_zones_by_type(self, zone_type: ZoneType) -> List[Zone]:
        """获取指定类型的所有分区"""
        return [zone for zone in self.zones if zone.zone_type == zone_type]
    
    def get_building_requirements(self, position: Point) -> Dict:
        """获取指定位置的建筑要求"""
        zone = self.get_zone_at_position(position)
        if zone:
            return {
                'allowed_types': zone.get_allowed_building_types(),
                'max_density': zone.get_building_density(),
                'height_limit': zone.get_height_limit(),
                'zone_type': zone.zone_type.value
            }
        
        # 默认要求（无分区）
        return {
            'allowed_types': ['residential', 'commercial'],
            'max_density': 0.4,
            'height_limit': 8,
            'zone_type': 'mixed'
        }
