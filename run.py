#!/usr/bin/env python3
"""
城市地图生成器 - 一键启动脚本

这个脚本提供了一个简单的交互式界面来生成城市地图
"""

import os
import sys
import subprocess
from typing import Dict, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        ('numpy', 'numpy'),
        ('matplotlib', 'matplotlib'),
        ('PIL', 'Pillow')  # PIL是Pillow的导入名
    ]
    missing_packages = []

    for import_name, package_name in required_packages:
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        install = input("\n是否自动安装依赖? (y/n): ").lower().strip()
        if install == 'y':
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install'] + missing_packages)
                print("依赖安装完成!")
            except subprocess.CalledProcessError:
                print("依赖安装失败，请手动安装:")
                print(f"pip install {' '.join(missing_packages)}")
                return False
        else:
            print("请手动安装依赖后再运行程序")
            return False
    
    return True


def show_menu():
    """显示主菜单"""
    print("\n" + "="*50)
    print("🏙️  城市地图生成器")
    print("="*50)
    print("1. 快速生成 - 使用默认设置")
    print("2. 小城市 - 300x300")
    print("3. 中等城市 - 500x500") 
    print("4. 大城市 - 800x800")
    print("5. 密集城市 - 高建筑密度")
    print("6. 绿色城市 - 更多公园")
    print("7. 超密集城市 - 最高道路密度")
    print("8. 自定义设置")
    print("9. 批量生成多个城市")
    print("10. 查看示例")
    print("0. 退出")
    print("="*50)


def get_custom_settings() -> Dict[str, Any]:
    """获取自定义设置"""
    settings = {}
    
    print("\n自定义城市设置:")
    print("(直接按回车使用默认值)")
    
    # 基本设置
    width = input("地图宽度 (默认: 500): ").strip()
    if width:
        settings['width'] = float(width)
    
    height = input("地图高度 (默认: 500): ").strip()
    if height:
        settings['height'] = float(height)
    
    name = input("城市名称 (默认: Generated City): ").strip()
    if name:
        settings['name'] = name
    
    seed = input("随机种子 (默认: 随机): ").strip()
    if seed:
        settings['seed'] = int(seed)
    
    # 密度设置
    print("\n密度设置 (0.0-1.0):")
    
    building_density = input("建筑密度 (默认: 0.4): ").strip()
    if building_density:
        settings['building_density'] = float(building_density)
    
    park_density = input("公园密度 (默认: 0.15): ").strip()
    if park_density:
        settings['park_density'] = float(park_density)
    
    road_density = input("道路密度 (默认: 0.3): ").strip()
    if road_density:
        settings['road_density'] = float(road_density)
    
    return settings


def run_generator(config_type: str = None, custom_settings: Dict[str, Any] = None, 
                 multiple: int = None):
    """运行地图生成器"""
    cmd = [sys.executable, 'main.py']
    
    if config_type:
        cmd.extend(['--config', config_type])
    
    if custom_settings:
        for key, value in custom_settings.items():
            if key == 'width':
                cmd.extend(['--width', str(value)])
            elif key == 'height':
                cmd.extend(['--height', str(value)])
            elif key == 'name':
                cmd.extend(['--name', str(value)])
            elif key == 'seed':
                cmd.extend(['--seed', str(value)])
            elif key == 'building_density':
                cmd.extend(['--building-density', str(value)])
            elif key == 'park_density':
                cmd.extend(['--park-density', str(value)])
            elif key == 'road_density':
                cmd.extend(['--road-density', str(value)])
    
    if multiple:
        cmd.extend(['--multiple', str(multiple)])
    
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"生成失败: {e}")
    except KeyboardInterrupt:
        print("\n用户中断操作")


def show_examples():
    """显示使用示例"""
    print("\n" + "="*50)
    print("📖 使用示例")
    print("="*50)
    
    examples = [
        ("生成默认城市", "python main.py"),
        ("生成小城市", "python main.py --config small"),
        ("生成密集城市", "python main.py --config dense"),
        ("自定义尺寸", "python main.py --width 800 --height 600"),
        ("指定名称和种子", "python main.py --name '我的城市' --seed 12345"),
        ("生成5个城市", "python main.py --multiple 5"),
        ("不显示窗口", "python main.py --no-show"),
        ("保存到指定目录", "python main.py --output ./my_maps"),
    ]
    
    for desc, cmd in examples:
        print(f"{desc}:")
        print(f"  {cmd}")
        print()
    
    input("按回车键返回主菜单...")


def main():
    """主函数"""
    print("正在检查依赖...")
    if not check_dependencies():
        return 1
    
    # 导入城市生成器模块
    try:
        from city_map_generator import CityMapGenerator
        print("✅ 城市地图生成器已就绪!")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return 1
    
    while True:
        show_menu()
        
        try:
            choice = input("\n请选择操作 (0-9): ").strip()
            
            if choice == '0':
                print("再见! 👋")
                break
            
            elif choice == '1':
                print("🚀 快速生成城市...")
                run_generator()
            
            elif choice == '2':
                print("🏘️ 生成小城市...")
                run_generator('small')
            
            elif choice == '3':
                print("🏙️ 生成中等城市...")
                run_generator('medium')
            
            elif choice == '4':
                print("🌆 生成大城市...")
                run_generator('large')
            
            elif choice == '5':
                print("🏢 生成密集城市...")
                run_generator('dense')
            
            elif choice == '6':
                print("🌳 生成绿色城市...")
                run_generator('green')
            
            elif choice == '7':
                print("⚙️ 自定义设置...")
                settings = get_custom_settings()
                run_generator(custom_settings=settings)
            
            elif choice == '8':
                count = input("要生成多少个城市? (默认: 3): ").strip()
                count = int(count) if count else 3
                print(f"🏭 批量生成 {count} 个城市...")
                run_generator(multiple=count)
            
            elif choice == '9':
                show_examples()
            
            else:
                print("❌ 无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n用户中断操作")
            break
        except ValueError as e:
            print(f"❌ 输入错误: {e}")
        except Exception as e:
            print(f"❌ 发生错误: {e}")
    
    return 0


if __name__ == "__main__":
    exit(main())
